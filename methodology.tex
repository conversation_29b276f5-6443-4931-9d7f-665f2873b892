\chapter{Methodology: Estimating DFSV Models via Bellman Information Filtering}\label{ch:methodology}

In this chapter, I detail the methodology for estimating Dynamic Factor Stochastic Volatility (DFSV) models using the Bellman Information Filter (BIF). First, I specify the DFSV model, which captures common movements in asset returns via latent factors while accommodating time-varying volatility. However, the model's non-linear, non-Gaussian structure presents estimation challenges. Subsequently, I introduce the BIF, an efficient state estimation approach offering potential computational and numerical stability advantages over traditional filters \parencite{lange_bellman_2024}.

\section{The Dynamic Factor Stochastic Volatility (DFSV) Model Specification}\label{sec:dfsv_model}

The DFSV model employs a state-space formulation to capture common movements in asset returns via latent factors and accommodates time-varying factor volatility, which governs the dynamics of asset co-movement. It comprises an observation equation linking returns to factors and state evolution equations for the latent factors and their volatilities.

\paragraph{Observation Equation:}
Asset returns, represented by the $N$-dimensional vector $\vr_t$, are generated via a linear factor model incorporating idiosyncratic noise:
\begin{align}
\label{obs_eq}
\vr_t &= \mLambda \vf_t + \vepsilon_t, \quad \vepsilon_t \sim \mathcal{N}\bigl(\vzeros,\ \mSigma_{\epsilon}\bigr),\\
\mSigma_\epsilon&=\Diag(\sigma_{1}^2,\dots,\sigma_{N}^2).
\end{align}
Where:
\begin{itemize}
    \item $\mLambda$ is an $N \times K$ matrix of factor loadings.
    \item $\vf_t = (f_{1,t},\dots,f_{K,t})' \in \mathbb{R}^K$ is the vector of latent factors, with $K\ll N$, that captures the common variation among asset returns.
    \item $\vepsilon_t$ represents the idiosyncratic errors with a constant diagonal covariance matrix $\mSigma_\epsilon$. This structure implies that the model primarily captures factor-driven volatility and co-movement, and does not explicitly model time-varying asset-specific idiosyncratic volatility.
\end{itemize}

\paragraph{Factor Evolution:}
To capture common dynamics, the latent factor vector $\vf_t$ follows a VAR(1) process featuring stochastic volatility:
\begin{equation}
\label{factor_AR}
\vf_{t+1} = \mPhi_f \,\vf_t + \vnu_{t+1}, \quad \vnu_{t+1} \sim \mathcal{N}\!\left(\vzeros,\ \Diag\Bigl(e^{h_{1,t+1}},\dots,e^{h_{K,t+1}}\Bigr)\right).
\end{equation}
Where $\mPhi_f$ governs factor persistence and spill-over. The innovation covariance is time-varying and depends on the latent log-volatility $h_{k,t+1}$ for $k=1,\dots,K$.

\paragraph{Log-Volatility Dynamics:}
Volatility dynamics are modeled jointly for the factors' latent log-volatilities, $\vh_t = (h_{1,t},h_{2,t},\ldots,h_{K,t})^\top$, which also follow a VAR(1) process:
\begin{equation}
\label{vol_VAR}
\vh_{t+1} = \vmu + \mPhi_h\,(\vh_t - \vmu) + \veta_{t+1}, \quad \veta_{t+1}\sim\mathcal{N}\!\left(\vzeros,\, \mQ_h\right).
\end{equation}
Here,
\begin{itemize}
    \item $\vmu \in \mathbb{R}^{K}$ is the long-run mean vector of the log-volatilities,
    \item $\mPhi_h \in \mathbb{R}^{K\times K}$ is an autoregressive coefficient matrix that captures both the persistence in each factor's volatility and cross-factor spillovers,
    \item $\mQ_h \in \mathbb{R}^ {K\times K}$ is a positive-definite covariance matrix governing the volatility shocks.
\end{itemize}

\paragraph{Complete State Vector:}
Combining these components, the full state vector $\valpha_t$ encompasses both factors and their log-volatilities:
\begin{equation}
\valpha_t = \begin{pmatrix} \vf_t \\ \vh_t \end{pmatrix} \in \mathbb{R}^{2K}.
\end{equation}
This combines the latent factors and their corresponding log-volatilities into a single vector for state-space modeling.






\subsection{Observation Likelihood Formulations}\label{sec:likelihoods}

Given the observation equation \eqref{obs_eq}, two distinct log-likelihood formulations are relevant, used differently by the BIF and PF.

First, the conditional log-likelihood of the observation $\vr_t$ given a specific factor realization $\vf_t$ depends solely on the idiosyncratic noise distribution:
\begin{equation}
\label{eq:cond_log_likelihood}
\log p(\vr_t \mid \vf_t) = -\frac{1}{2}\left[N\log(2\pi) + \log|\mSigma_\epsilon| + (\vr_t - \mLambda\vf_t)'\mSigma_\epsilon^{-1}(\vr_t - \mLambda\vf_t)\right].
\end{equation}
The Particle Filter benchmark (Section~\ref{sec:particle_filter}) directly employs this standard conditional log-likelihood \eqref{eq:cond_log_likelihood}. This choice is inherent to the PF's mechanism, where importance weights for each particle $\valpha_t^{(i)}$ are calculated based on how well that specific particle's state explains the observation $\vr_t$, i.e., based on $p(\vr_t | \valpha_t^{(i)})$, which simplifies to $p(\vr_t | \vf_t^{(i)})$ given the model structure.

Second, the Bellman Information Filter (BIF) leverages a different calculation, denoted $\ell(\vr_t \mid \valpha_t)$, within its state update objective (Section~\ref{sec:bif_update}) and its pseudo-likelihood for parameter estimation (Section~\ref{sec:bif_pseudo_likelihood}). This term incorporates the covariance arising from both the idiosyncratic noise and the uncertainty about the factors $\vf_t$, conditional on the volatility state $\vh_t$:
\begin{equation}
\label{eq:log_likelihood}
\ell(\vr_t \mid \valpha_t) = -\frac{1}{2}\left[N\log(2\pi) + \log|\mSigma_t| + (\vr_t - \mLambda\vf_t)'\mSigma_t^{-1}(\vr_t - \mLambda\vf_t)\right],
\end{equation}
where the marginal observation covariance conditional on $\vh_t$ is
\begin{equation}
\label{eq:marginal_cov}
\mSigma_t = \text{Var}(\vr_t \mid \vh_t) = \mLambda\text{Var}(\vf_t \mid \vh_t)\mLambda' + \text{Var}(\vepsilon_t) = \mLambda\Diag(e^{\vh_t})\mLambda' + \mSigma_\epsilon.
\end{equation}
Using the marginal covariance $\mSigma_t$ \eqref{eq:marginal_cov} within the likelihood term \eqref{eq:log_likelihood} is crucial for the BIF framework. It ensures that the optimization performed during the filter update correctly accounts for the information $\vr_t$ provides about the full state $\valpha_t = [\vf_t', \vh_t']'$, including the influence of the volatility state $\vh_t$. Furthermore, it maintains consistency with the structure of the information matrix updates derived from BIF theory \parencite{lange_bellman_2024}. Appendix~\ref{app:bf_loglik} provides detailed derivations for both likelihood formulations.

\subsection{Model Assumptions}\label{sec:model_assumptions}

The specified DFSV model rests on several key assumptions:

\begin{itemize}
    \item \textbf{Idiosyncratic Error Structure ($\mSigma_\epsilon$):} Following standard practice \parencite{aguilar_bayesian_2000} and to maintain tractability, I assume idiosyncratic errors are uncorrelated across assets and have constant variance ($\mSigma_\epsilon$ is diagonal and constant over time, Equation~\ref{obs_eq}). This implies the model primarily captures volatility driven by common factors (co-movement) and relies on the latent factors and their dynamic stochastic volatilities (via $\mPhi_f$, $\mPhi_h$, and $\vh_t$ influencing $\mSigma_t$ in Equation~\ref{eq:marginal_cov}) to explain the time-varying covariance structure. While simplifying estimation, this assumption is a limitation for capturing the full dynamics of individual asset returns, as it neglects potential asset-specific time-varying volatility. This limitation is revisited during the empirical evaluation (Chapter~\ref{ch:empirical_application}).

    \item \textbf{Factor Innovation Structure ($\vnu_{t+1}$):} The model assumes factor innovations $\vnu_{t+1}$ have a diagonal covariance matrix conditional on $\vh_{t+1}$ (Equation~\ref{factor_AR}). This interprets the latent factors $\vf_t$ as distinct risk sources with uncorrelated contemporaneous shocks. Dynamic interactions and lagged cross-dependencies (between factors, and between factors and volatilities) are captured by the potentially non-diagonal autoregressive matrices $\mPhi_f$ and $\mPhi_h$.

    \item \textbf{Error Distributions:} I assume all error terms ($\vepsilon_t$, $\vnu_{t+1}$, $\veta_{t+1}$) follow Gaussian distributions. While mathematically convenient, this simplifies reality, as financial returns often exhibit heavy tails and skewness. Extending the model to non-Gaussian errors (e.g., Student's t) is a potential area for future research.

    \item \textbf{Identification Constraint and Factor Ordering:} The lower-triangular constraint on $\mLambda$ (Section~\ref{sec:identification}) ensures model identification. This structure, however, imposes an inherent ordering on the factors. Consequently, I identify factors statistically rather than comparing them directly to pre-defined economic factors (e.g., Fama-French). The interpretation of these statistical factors and potential sensitivity to ordering are discussed further in the empirical analysis (Chapter~\ref{ch:empirical_application}).
\end{itemize}
These assumptions facilitate tractable DFSV estimation within the BIF framework while allowing the model considerable flexibility to capture complex financial time series dynamics.

\subsection{Parameterization, Constraints, and Identification}\label{sec:identification}

To ensure model identifiability and stable estimation, I impose constraints on the factor loading matrix $\mLambda$ and the autoregressive matrices $\mPhi_f, \mPhi_h$. Without constraints, the model suffers from rotational/scale indeterminacy ($\mLambda$) and potential non-stationarity ($\mPhi_f, \mPhi_h$).

\paragraph{Lower-Triangular Constraint:}
I adopt a lower-triangular constraint with unit diagonal elements for the first $K$ rows of $\mLambda$:
\begin{equation}
\mLambda = \begin{bmatrix}
1 & 0 & \cdots & 0 \\
\lambda_{21} & 1 & \cdots & 0 \\
\vdots & \vdots & \ddots & \vdots \\
\lambda_{K1} & \lambda_{K2} & \cdots & 1 \\
\lambda_{K+1,1} & \lambda_{K+1,2} & \cdots & \lambda_{K+1,K} \\
\vdots & \vdots & \ddots & \vdots \\
\lambda_{N1} & \lambda_{N2} & \cdots & \lambda_{NK}
\end{bmatrix}
\end{equation}
This constraint ensures identification and aids interpretation by resolving factor indeterminacy and providing a clear role for each factor (e.g., the first factor affects all assets, the second affects all but the first, etc.). Such uniqueness is essential for consistent estimation.

\paragraph{Stability Constraints:}
For stationarity, the eigenvalues of both $\mPhi_f$ and $\mPhi_h$ must lie within the unit circle. Section~\ref{sec:param_estimation} details how I enforce this during parameter estimation using transformations and penalties.

\section{State Estimation via Bellman Information Filter (BIF)}\label{sec:bif_estimation}\label{sec:bif_filter}

I estimate the latent state $\valpha_t$, which governs the dynamic factor structure and co-movement in the DFSV model, using the Bellman Information Filter (BIF), adapting the methodology from \textcite{lange_bellman_2024}. Although \citeA{lange_bellman_2024} derives both covariance (BF) and information (BIF) forms, I employ the BIF due to its enhanced numerical stability for the DFSV model, particularly with state-dependent volatility. The BIF propagates the information state (mode and precision matrix) rather than the traditional mean and covariance.

\subsection{BIF Prediction Step}

The BIF prediction step adapts the standard Kalman filter prediction equations to propagate the information state (mode and precision matrix) forward in time according to the model dynamics.

\paragraph{State Transition Matrix:}
The state transition matrix $\mT$ for the DFSV model is block-diagonal, incorporating the factor dynamics and log-volatility dynamics:
\begin{equation}
\label{eq:transition_matrix}
\mT = \begin{bmatrix} \mPhi_f & \vzeros \\ \vzeros & \mPhi_h \end{bmatrix}.
\end{equation}
Where $\mPhi_f$ governs the factor dynamics and $\mPhi_h$ governs the log-volatility dynamics as specified in Equations \eqref{factor_AR} and \eqref{vol_VAR}.

\paragraph{State Prediction:}
The predicted state mean $\hat{\valpha}_{t|t-1}$ is calculated by applying the state transition matrix to the posterior state estimate from the previous time step. For the DFSV model, this involves separate predictions for the factors and log-volatilities:
\begin{align}
\label{eq:state_pred_factors}
\hat{\vf}_{t|t-1} &= \mPhi_f\,\hat{\vf}_{t-1|t-1}, \\
\label{eq:state_pred_logvols}
\hat{\vh}_{t|t-1} &= \vmu + \mPhi_h\,(\hat{\vh}_{t-1|t-1} - \vmu).
\end{align}
These are combined to form the complete predicted state vector:
\begin{equation}
\label{eq:state_pred_full}
\hat{\valpha}_{t|t-1} = \begin{bmatrix} \hat{\vf}_{t|t-1} \\ \hat{\vh}_{t|t-1} \end{bmatrix}.
\end{equation}

\paragraph{Innovation Covariance:}
The covariance matrix of state innovations, $\hat{\mQ}_{t|t-1}$, is block-diagonal, incorporating the time-varying volatility of factor innovations and the constant volatility of log-volatility shocks:
\begin{equation}
\label{eq:innov_cov}
\hat{\mQ}_{t|t-1}= \begin{bmatrix} \Diag\Bigl(e^{\hat{h}_{1,t|t-1}}, \dots, e^{\hat{h}_{K,t|t-1}}\Bigr) & \vzeros \\ \vzeros & \mQ_h \end{bmatrix}.
\end{equation}
This structure reflects the model's assumption that factor innovations have stochastic volatilities determined by the current log-volatility states, while log-volatility innovations have constant covariance $\mQ_h$.

\paragraph{Covariance Prediction:}
In the standard Kalman filter, the predicted covariance would be calculated as:
\begin{equation}
\label{eq:cov_pred}
\hat{\mP}_{t|t-1} = \mT\,\hat{\mP}_{t-1|t-1}\,\mT' + \hat{\mQ}_{t|t-1}.
\end{equation}
However, the BIF operates in the information space, using precision matrices instead of covariance matrices for improved numerical stability.

\paragraph{Information Prediction (Joseph Form):}
The BIF propagates the precision matrix $\boldsymbol{\Omega}_{t|t-1} = \hat{\mP}_{t|t-1}^{-1}$ instead of the covariance matrix. The prediction step for the information matrix employs the Joseph form:
\begin{equation}
\boldsymbol{\Omega}_{t|t-1} = \boldsymbol{Q}_t^{-1} - \boldsymbol{Q}_t^{-1}\mT\boldsymbol{M}^{-1}\mT^\top\boldsymbol{Q}_t^{-1},
\end{equation}
where $\boldsymbol{M} = \boldsymbol{\Omega}_{t-1|t-1} + \mT^\top\boldsymbol{Q}_t^{-1}\mT$. This formulation, based on the Woodbury matrix identity, avoids directly inverting the posterior information matrix, enhancing numerical stability particularly for high-dimensional state spaces. It reduces computational complexity from $O(N^3)$ to $O(NK^2 + K^3)$ when $K \ll N$, making it especially efficient for DFSV models (see Appendix~\ref{app:matrix_identities} for details).

\subsection{BIF Update Step: Posterior Mode Optimization via Block Coordinate Descent}\label{sec:bif_update}

When a new observation $\vr_t$ arrives, the BIF update step finds the posterior mode $\hat{\valpha}_{t|t}$ by maximizing the log-posterior density $p(\valpha_t | \vr_t, \mathcal{F}_{t-1})$, where $\mathcal{F}_{t-1}$ denotes the information set up to time $t-1$. This log-posterior is proportional to the sum of the observation log-likelihood and the predicted state log-density:
\begin{equation}
\log p(\valpha_t | \vr_t, \mathcal{F}_{t-1}) \propto \ell(\vr_t | \valpha_t) + \log p(\valpha_t | \mathcal{F}_{t-1}).
\end{equation}
The BIF approximates the predicted state density $p(\valpha_t | \mathcal{F}_{t-1})$ as Gaussian with mean $\hat{\valpha}_{t|t-1}$ and precision $\boldsymbol{\Omega}_{t|t-1}$. Therefore, the update step maximizes the objective function:
\begin{equation}\label{eq:objective_function}
J(\valpha_t) = \ell(\vr_t \mid \valpha_{t}) - \frac{1}{2}\left(\valpha_t - \hat{\valpha}_{t|t-1}\right)' \boldsymbol{\Omega}_{t|t-1}\left(\valpha_t - \hat{\valpha}_{t|t-1}\right).
\end{equation}
The posterior mode is then:
\begin{equation}
\hat{\valpha}_{t|t} = \arg\max_{\valpha_t \in \mathbb{R}^{2K}} J(\valpha_t).
\end{equation}
This optimization combines the marginal observation log-likelihood \eqref{eq:log_likelihood} with a quadratic penalty incorporating the prior information from the prediction step. However, the non-linear dependence of the likelihood on $\vh_t$ (through $\mSigma_t$ in \eqref{eq:marginal_cov}) makes direct optimization over the full state $\valpha_t$ challenging. To handle this efficiently, I employ a Block Coordinate Descent (BCD) algorithm, which iteratively optimizes blocks of the state vector ($\vf_t$ and $\vh_t$).

\paragraph{Block Coordinate Descent Algorithm:}
The BCD algorithm alternates between updating factors $\vf_t$ (holding $\vh_t$ fixed) and log-volatilities $\vh_t$ (holding $\vf_t$ fixed), as outlined in Algorithm~\ref{alg:bcd_update}. This leverages the problem's structure: the objective function is quadratic in $\vf_t$ (allowing a closed-form update) but non-linear in $\vh_t$ (requiring numerical optimization). Full derivations are in Appendices~\ref{app:factor_update} and \ref{app:logvol_update}.

\begin{algorithm}[H]
\caption{Block Coordinate Descent for BIF Update Step}
\label{alg:bcd_update}
\begin{algorithmic}[1]

\Require Factor loadings $\mLambda$, idiosyncratic variances $\mSigma_\epsilon$, predicted state $\hat{\valpha}_{t|t-1}$, predicted information matrix $\boldsymbol{\Omega}_{t|t-1}$, observation $\vr_t$, maximum iterations $\text{max\_iters}$.
\State \textbf{Initialize} $\vf^{(1)} \leftarrow \hat{\vf}_{t|t-1}$, $\vh^{(1)} \leftarrow \hat{\vh}_{t|t-1}$
\For{$k = 1$ to $\text{max\_iters}$}
  \State \textit{Update Factors:} Fix $\vh^{(k)}$, compute $\mSigma_t^{(k)} = \mLambda\,\Diag(e^{\vh^{(k)}})\,\mLambda^\top + \mSigma_\epsilon$.
  \State Solve linear system for $\vf^{(k+1)}$:
  \State $\vf^{(k+1)} \leftarrow \left(\mLambda^\top (\mSigma_t^{(k)})^{-1}\mLambda + \boldsymbol{\Omega}_{ff}\right)^{-1} \left(\mLambda^\top (\mSigma_t^{(k)})^{-1}\vr_t + \boldsymbol{\Omega}_{ff}\hat{\vf}_{t|t-1} + \boldsymbol{\Omega}_{fh}(\vh^{(k)} - \hat{\vh}_{t|t-1})\right)$ \Comment{See Appendix~\ref{app:factor_update}}
  \State \textit{Update Log-Volatilities:} Fix $\vf^{(k+1)}$.
  \State Solve non-linear optimization for $\vh^{(k+1)}$ using BFGS:
  \State $\vh^{(k+1)} \leftarrow \arg\max_{\vh} J([\vf^{(k+1)\prime}, \vh^{\prime}]^{\prime})$ \Comment{See Appendix~\ref{app:logvol_update}}
\EndFor
\State \textbf{Return} $\hat{\valpha}_{t|t} = [\vf^{(\text{final})'}, \vh^{(\text{final})'} ]'$
\end{algorithmic}
\end{algorithm}

\paragraph{Information Matrix Update:}
After finding the posterior mode $\hat{\valpha}_{t|t}$, I update the information matrix using the Expected Fisher Information Matrix (E-FIM):
\begin{equation}
\boldsymbol{\Omega}_{t|t} = \boldsymbol{\Omega}_{t|t-1} + \Infmat_t,
\end{equation}
where $\Infmat_t$ is the E-FIM evaluated at $\hat{\valpha}_{t|t}$. The E-FIM exhibits a block-diagonal structure due to factors affecting only the mean and log-volatilities affecting only the covariance of the observation distribution:
\begin{equation}
\Infmat_t = \begin{bmatrix}
\Infmat_{ff} & \boldsymbol{0} \\
\boldsymbol{0} & \Infmat_{hh}
\end{bmatrix} = \begin{bmatrix}
\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda & \boldsymbol{0} \\
\boldsymbol{0} & \frac{1}{2}\left[e^{h_{i,t} + h_{j,t}}[\Infmat_{ff}]_{i,j}^2\right]_{i,j}
\end{bmatrix}.
\end{equation}
I provide a detailed derivation in Appendix~\ref{app:fim}. Using the E-FIM instead of the Observed Fisher Information Matrix (O-FIM) guarantees positive semi-definiteness, enhancing numerical stability and avoiding the need for regularization techniques like eigenvalue clipping.

\paragraph{Computational Efficiency:}
The BCD algorithm provides significant computational advantages for high-dimensional DFSV models. The implementation leverages matrix identities (Appendix~\ref{app:matrix_identities}), dimensionality reduction, and efficient optimization techniques to achieve stability and speed. Appendix~\ref{app:bcd_efficiency} discusses these computational aspects in detail.

\section{Benchmark State Estimation via Particle Filter (PF)}\label{sec:particle_filter}

To benchmark the BIF's performance, I also implement a Particle Filter (PF) for the DFSV model. The PF is a sequential Monte Carlo method that approximates the filtering distribution using a set of weighted particles.

\subsection{Bootstrap Filter / SISR Implementation}

Specifically, I use the Bootstrap Filter, a common Sequential Importance Sampling with Resampling (SISR) algorithm where the proposal distribution is simply the state transition density $p(\valpha_t | \valpha_{t-1})$. The standard Bootstrap filter was chosen as a widely recognized baseline for comparison due to its relative ease of implementation and established ability to handle complex dynamics in moderately dimensioned problems, despite known limitations such as potential particle impoverishment, which more advanced particle filters aim to mitigate. Algorithm~\ref{alg:pf_bootstrap} outlines the core steps.

\begin{algorithm}[H]
\caption{Bootstrap Particle Filter for DFSV Model}
\label{alg:pf_bootstrap}
\begin{algorithmic}[1]

\Require Model parameters $\mTheta$, observations $\{\vr_t\}_{t=1}^T$, number of particles $P$, resampling threshold $\text{ESS}_{\text{threshold}}$
\State \textbf{Initialize} particles $\{\valpha_0^{(i)}\}_{i=1}^P$ and weights $\{w_0^{(i)} = 1/P\}_{i=1}^P$
\For{$t = 1$ to $T$}
  \State \textbf{Predict:} Propagate particles $\{\valpha_{t-1}^{(i)}\}_{i=1}^P$ using state transition equations \eqref{factor_AR} and \eqref{vol_VAR} to obtain $\{\valpha_t^{(i)}\}_{i=1}^P$.
  \State \textbf{Update:} Compute unnormalized importance weights using the conditional observation log-likelihood (Equation~\ref{eq:cond_log_likelihood}):
  \State $\tilde{w}_t^{(i)} \leftarrow w_{t-1}^{(i)} \cdot \exp(\log p(\vr_t | \valpha_t^{(i)}))$
  \State \textbf{Normalize} weights: $w_t^{(i)} \leftarrow \tilde{w}_t^{(i)} / \sum_{j=1}^P \tilde{w}_t^{(j)}$
  \State \textbf{Calculate} Effective Sample Size (ESS): $\text{ESS}_t \leftarrow 1 / \sum_{i=1}^P (w_t^{(i)})^2$
  \If{$\text{ESS}_t < \text{ESS}_{\text{threshold}}$}
    \State \textbf{Resample} particles $\{\valpha_t^{(i)}\}_{i=1}^P$ with weights $\{w_t^{(i)}\}_{i=1}^P$ using systematic resampling.
    \State \textbf{Reset} weights: $w_t^{(i)} \leftarrow 1/P$ for all $i$.
  \EndIf
  \State \textbf{Compute} filtered state estimate: $\hat{\valpha}_{t|t} \leftarrow \sum_{i=1}^P w_t^{(i)} \valpha_t^{(i)}$
\EndFor
\end{algorithmic}
\end{algorithm}

\paragraph{Systematic Resampling:}
To combat particle degeneracy (where a few particles dominate the weight distribution), I perform systematic resampling whenever the Effective Sample Size (ESS) falls below a threshold (e.g., $P/2$). Systematic resampling selects particles based on a single random draw mapped onto the cumulative weight distribution, generally offering better state space coverage than simpler multinomial resampling.

\paragraph{Likelihood Calculation for Parameter Optimization:}
The PF also yields an estimate of the model's likelihood, essential for parameter optimization via MLE. The log-likelihood is approximated by summing the log of the average importance weights at each time step:
\begin{equation}\label{eq:pf_loglik}
\log p(\vr_{1:T} | \mTheta) \approx \sum_{t=1}^{T} \log \left( \frac{1}{P}\sum_{i=1}^{P} \frac{\tilde{w}_t^{(i)}}{w_{t-1}^{(i)}} \right) = \sum_{t=1}^{T} \log \left( \frac{1}{P}\sum_{i=1}^{P} p(\vr_t | \valpha_t^{(i)}) \right).
 \end{equation}
The conditional likelihood $p(\vr_t | \valpha_t^{(i)})$ is computed efficiently using Equation~\eqref{eq:cond_log_likelihood} and leveraging the diagonal structure of $\mSigma_\epsilon$:
\begin{align}
\log p(\vr_t | \valpha_t^{(i)}) &= -\frac{1}{2}\left[N\log(2\pi) + \log|\mSigma_\epsilon| + (\vr_t - \mLambda\vf_t^{(i)})'\mSigma_\epsilon^{-1}(\vr_t - \mLambda\vf_t^{(i)})\right] \\
&= -\frac{1}{2}\left[N\log(2\pi) + \sum_{j=1}^{N}\log(\sigma_j^2) + \sum_{j=1}^{N}\frac{(r_{j,t} - [\mLambda\vf_t^{(i)}]_j)^2}{\sigma_j^2}\right].
\end{align}
This accumulated log-likelihood serves as the objective function for Maximum Likelihood Estimation (MLE) of the model parameters $\mTheta$.


\section{Parameter Estimation}\label{sec:param_estimation}

Beyond state estimation, the static parameters $\mTheta = (\mLambda, \mPhi_f, \mPhi_h, \vmu, \mQ_h, \mSigma_\epsilon)$ must be estimated from the data. For the BIF, this involves maximizing an approximate pseudo-likelihood function derived from the filter outputs. For the PF, standard Maximum Likelihood Estimation (MLE) using the particle-based likelihood approximation (Equation~\ref{eq:pf_loglik}) is used.

\subsection{BIF Pseudo-Likelihood Maximization}\label{sec:bif_pseudo_likelihood}

Following \textcite{lange_bellman_2024}, I estimate the parameters $\mTheta$ by maximizing the BIF's approximate pseudo-likelihood function:
\begin{align}\label{eq:bif_pseudo_lik}
\mathcal{L}(\mTheta) \approx \sum_{t=1}^{T} \left\{ \ell(\vr_t \mid \hat{\valpha}_{t|t}, \mTheta)
- \frac{1}{2}\log \frac{\det(\hat{\mP}_{t|t-1})}{\det(\hat{\mP}_{t|t})}
- \frac{1}{2}\left(\hat{\valpha}_{t|t} - \hat{\valpha}_{t|t-1}\right)' \hat{\mP}_{t|t-1}^{-1}\left(\hat{\valpha}_{t|t} - \hat{\valpha}_{t|t-1}\right) \right\}.
\end{align}
This function combines the marginal observation likelihood $\ell(\cdot)$ \eqref{eq:log_likelihood} evaluated at the filtered state $\hat{\valpha}_{t|t}$ with terms accounting for the information gain during the update step, represented by the predicted and filtered state estimates ($\hat{\valpha}_{t|t-1}, \hat{\valpha}_{t|t}$) and their associated covariance/information matrices. This pseudo-likelihood is exact for linear Gaussian models and provides a second-order approximation otherwise.

Rewriting Equation~\eqref{eq:bif_pseudo_lik} using information matrices ($\boldsymbol{\Omega} = \mP^{-1}$) yields the form used in the implementation:
\begin{align}
\mathcal{L}(\mTheta) \approx \sum_{t=1}^{T} \left\{ \ell(\vr_t \mid \hat{\valpha}_{t|t}, \mTheta)
- \frac{1}{2}\log \frac{\det(\boldsymbol{\Omega}_{t|t})}{\det(\boldsymbol{\Omega}_{t|t-1})}
- \frac{1}{2}\left(\hat{\valpha}_{t|t} - \hat{\valpha}_{t|t-1}\right)' \boldsymbol{\Omega}_{t|t-1}\left(\hat{\valpha}_{t|t} - \hat{\valpha}_{t|t-1}\right) \right\}.
\end{align}
Parameter estimation maximizes this pseudo-likelihood: $\hat{\mTheta} = \arg\max_{\mTheta} \mathcal{L}(\mTheta)$.

\subsection{Parameter Transformations and Stationarity Constraints}

To facilitate unconstrained optimization while respecting model constraints, I employ parameter transformations and stability enforcement techniques.\footnote{The inverse mappings are $\text{atanh}(x) = \frac{1}{2}\log\left(\frac{1+x}{1-x}\right)$ and $\text{softplus}^{-1}(y) = \log(e^y - 1)$.}

\paragraph{Parameter Transformations:}
\begin{itemize}
    \item \textbf{Diagonal Tanh Transformation:} To constrain diagonal elements of $\mPhi_f$ and $\mPhi_h$ to $(-1,1)$, I use the inverse hyperbolic tangent ($\tanh$) transformation on the unconstrained parameters:
    \begin{equation}
    [\mPhi_f]_{ii} = \tanh([\mPhi_f]_{ii}^{\text{unc}}), \quad [\mPhi_h]_{ii} = \tanh([\mPhi_h]_{ii}^{\text{unc}}).
    \end{equation}
    This aids stability during optimization while permitting rich off-diagonal dynamics.

    \item \textbf{Variance Softplus Transformation:} To ensure positive variances ($\sigma_i^2$ in $\mSigma_\epsilon$, diagonal elements of $\mQ_h$), I apply the softplus function to the unconstrained parameters:
    \begin{equation}
    [\mSigma_\epsilon]_{ii} = \text{softplus}([\mSigma_\epsilon]_{ii}^{\text{unc}}), \quad [\mQ_h]_{ii} = \text{softplus}([\mQ_h]_{ii}^{\text{unc}}).
    \end{equation}
    Where $\text{softplus}(x) = \log(1 + e^x)$ maps $\mathbb{R} \to \mathbb{R}^+$.

    \item \textbf{Identification Constraint:} The lower-triangular structure of $\mLambda$ (Section~\ref{sec:identification}) is enforced directly during optimization to maintain identifiability.
\end{itemize}

\paragraph{Stability Enforcement via Eigenvalue Penalty:}
The tanh transformation alone does not guarantee stability for the full matrices $\mPhi_f$ and $\mPhi_h$. Therefore, I add a penalty term to the objective function that discourages eigenvalues from exceeding the unit circle:
\begin{equation}\label{eq:stability_penalty}
\mathcal{L}_{\text{penalized}}(\mTheta) = \mathcal{L}(\mTheta) - \lambda \cdot \left(\sum_{j=1}^{K} \max(0, |\lambda_j(\mPhi_f)| - (1-\epsilon)) + \sum_{j=1}^{K} \max(0, |\lambda_j(\mPhi_h)| - (1-\epsilon))\right).
\end{equation}
Here, $\lambda_j(\cdot)$ are eigenvalues, $\lambda$ is a penalty weight, and $\epsilon$ is a small buffer ($10^{-6}$). This penalty guides the optimization towards stationary solutions. I set $\lambda = 10^4$ throughout the simulations and empirical application (Chapters \ref{ch:simulation} and \ref{ch:empirical_application}).

\section{Computational Implementation Notes}\label{sec:comp_implementation}

The practical implementation leverages JAX \parencite{bradbury_jax_2018} for automatic differentiation, JIT compilation, and vectorization, enabling efficient gradient-based optimization and execution speed. Key algorithmic choices enhance efficiency for high-dimensional settings ($N \gg K$):
\begin{itemize}
    \item For the BIF, matrix identities (Appendix~\ref{app:matrix_identities}) reduce the complexity of crucial update steps from $O(N^3)$ to $O(NK^2 + K^3)$.
    \item The BIF's Block Coordinate Descent update (Section~\ref{sec:bif_update}) further improves efficiency by breaking the optimization into manageable subproblems.
    \item Both BIF and PF implementations utilize vectorized operations extensively.
\end{itemize}
Parameter estimation uses standard gradient-based optimizers, incorporating the transformations and stability penalties described in Section~\ref{sec:param_estimation}. All computations use 64-bit precision. This setup makes DFSV estimation feasible for the problem dimensions considered. The code is available on GitHub\footnote{Project repository: \url{https://github.com/givani30/BellmanFilterDFSV}}.

\section{Benchmark Model Specifications}\label{sec:methodology:benchmark_models}

To assess the performance of the DFSV model estimated via BIF (henceforth DFSV-BIF), I compare it against three benchmark models. This section briefly outlines their methodologies; Chapter~\ref{ch:empirical_application} provides specific configuration details used in the empirical study.

\subsection{DFSV Model with Particle Filter (DFSV-PF)}\label{sec:methodology:benchmark:dfsv_pf}

The first benchmark uses the identical DFSV model specification (Section~\ref{sec:dfsv_model}) but employs the Particle Filter (Section~\ref{sec:particle_filter}) for both state and parameter estimation (via MLE using Equation~\ref{eq:pf_loglik}). This allows a direct comparison of the BIF and PF estimation approaches while holding the underlying dynamic model constant, isolating the impact of the chosen filtering and estimation technique.

\subsection{Dynamic Conditional Correlation (DCC) GARCH}\label{sec:methodology:benchmark:dcc_garch}

The Dynamic Conditional Correlation (DCC) GARCH model \parencite{engle_dynamic_2002} represents a different paradigm for modeling time-varying covariance. Rather than relying on a factor structure, DCC-GARCH directly models the dynamics of the conditional correlations between assets after filtering out individual volatilities. The standard DCC(1,1) model involves two stages:
\begin{enumerate}
    \item \textbf{Univariate GARCH:} Fit GARCH(1,1) models to each return series $r_{i,t}$ to estimate individual conditional variances $\sigma_{i,t}^2$:
    \begin{equation}
    \sigma_{i,t}^2 = \omega_i + \alpha_i r_{i,t-1}^2 + \beta_i \sigma_{i,t-1}^2.
    \end{equation}
    \item \textbf{Dynamic Correlation:} Use standardized residuals $\boldsymbol{\varepsilon}_t$ from stage 1 to estimate a time-varying correlation matrix $\mathbf{R}_t$ via a proxy process $\mathbf{Q}_t$:
    \begin{align}
    \mathbf{Q}_t &= (1-a-b)\bar{\mathbf{Q}} + a(\boldsymbol{\varepsilon}_{t-1}\boldsymbol{\varepsilon}_{t-1}') + b\mathbf{Q}_{t-1}\\
    \mathbf{R}_t &= \text{diag}(\mathbf{Q}_t)^{-1/2}\mathbf{Q}_t\text{diag}(\mathbf{Q}_t)^{-1/2}.
    \end{align}
    Where $\bar{\mathbf{Q}}$ is the unconditional correlation matrix, and $a, b$ are scalar dynamics parameters.
\end{enumerate}
The full conditional covariance is $\mathbf{\Sigma}_t = \text{diag}(\sigma_{1,t}, \ldots, \sigma_{N,t})\mathbf{R}_t\text{diag}(\sigma_{1,t}, \ldots, \sigma_{N,t})$. Estimation typically uses two-step MLE.

\subsection{Dynamic Factor Model with Constant Volatility (DFM)}\label{sec:methodology:benchmark:dfm}

The third benchmark simplifies the DFSV model by removing the stochastic volatility components, resulting in a standard Dynamic Factor Model (DFM) with constant volatility. It retains the factor structure for returns but assumes constant covariance matrices for the innovations:
\begin{align}
\mathbf{r}_t &= \mathbf{\Lambda}\mathbf{f}_t + \boldsymbol{\varepsilon}_t, \quad \boldsymbol{\varepsilon}_t \sim \mathcal{N}(\mathbf{0}, \mathbf{\Sigma}_\varepsilon)\\
\mathbf{f}_{t+1} &= \mathbf{\Phi}_f\mathbf{f}_t + \boldsymbol{\nu}_{t+1}, \quad \boldsymbol{\nu}_{t+1} \sim \mathcal{N}(\mathbf{0}, \mathbf{\Sigma}_\nu).
\end{align}
Here, $\mathbf{\Sigma}_\varepsilon$ (diagonal) and $\mathbf{\Sigma}_\nu$ (potentially non-diagonal) are constant. This linear Gaussian state-space model is estimable via the standard Kalman filter and MLE, often used for dimension reduction and forecasting \parencite{bai_determining_2002}.

\section{Summary: Methodology and Contribution}\label{sec:methodology:summary}\label{sec:methodology:model_summary}

This chapter detailed the DFSV model and the BIF estimation framework adapted for it, including BCD optimization and stability constraints. Parameter estimation via pseudo-likelihood was outlined, alongside benchmark models (PF, DCC, DFM) and computational considerations.

The complete estimation pipeline involves preprocessing data, initializing parameters and states, iteratively running the chosen filtering algorithm (BIF or PF) to estimate latent states, computing the corresponding (pseudo-)likelihood, and optimizing this objective function to estimate static parameters. Multiple optimization runs with different initializations help ensure convergence towards a global optimum. While this framework offers a computationally feasible approach for high-dimensional DFSV estimation, its empirical adequacy, particularly concerning the assumption of constant idiosyncratic variance \parencite{aguilar_bayesian_2000}, is rigorously tested in Chapter~\ref{ch:empirical_application}.

Addressing Research Question 1, this chapter demonstrated how the BIF can be customized for high-dimensional DFSV models. Key adaptations include the Block Coordinate Descent (BCD) algorithm for the non-linear update step, integration of VAR dynamics for both factors and log-volatilities, and the use of the Expected Fisher Information Matrix (E-FIM) for stable information matrix updates. These innovations, combined with efficient computational techniques (Section~\ref{sec:comp_implementation} and appendices), yield an algorithm with computational complexity scaling favorably ($O(NK^2 + K^3)$), making it practical for applications where $N \gg K$. The subsequent chapters evaluate the empirical performance and utility of this framework using simulated (Chapter~\ref{ch:simulation}) and real financial data (Chapter~\ref{ch:empirical_application}).
