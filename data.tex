\chapter{Data, Evaluation, and Simulation Design}\label{ch:data_evaluation_simulation} % Updated Chapter Title and Label

% --- Start of New Sections ---
This chapter establishes the foundation for evaluating the proposed DFSV-BIF model. It introduces the empirical dataset, defines benchmark models and evaluation metrics for real-world comparison, and details simulation studies designed to test the filter's properties under controlled conditions.

\section{Empirical Data Description}\label{sec:empirical_data}

This section describes the Fama-French 100 portfolio dataset used for empirically testing the DFSV-BIF framework. The analysis centers on the monthly value-weighted returns of these portfolios, serving as the primary input.

\subsection{Data Sources, Selection, and Relevance}\label{sec:empirical_data:sources}

This study utilizes the value-weighted monthly returns of the '100 Portfolios Formed on Size and Book-to-Market' from the Kenneth R. French Data Library\footnote{Available at \url{https://mba.tuck.dartmouth.edu/pages/faculty/ken.french/data_library.html}}, a standard benchmark in asset pricing \cite{fama_common_1993}. The data span July 1963 to December 2023 ($T = 726$ observations). Its high dimensionality (initially $N=100$) offers a suitable testbed for factor models and facilitates evaluating the BIF's scalability. This sample period aligns with standard practice \cite{fama_common_1993}, ensuring data reliability. Appendix~\ref{app:portfolio_characteristics} provides further details on portfolio characteristics.

\subsection{Preprocessing and Exploratory Data Analysis (EDA)}\label{sec:empirical_data:preprocessing}

This section summarizes the exploratory data analysis; Appendix~\ref{app:data_supplementary} offers more detailed EDA results, including additional statistics and figures.

Preprocessing involved several steps. First, handling missing values ($-99.99$ converted to NaN) necessitated excluding five portfolios ('BIG HiBM', 'ME10 BM8', 'ME10 BM9', 'ME7 BM10', 'ME9 BM10') to ensure a balanced panel, resulting in $N=95$ portfolios. Second, percentage returns were converted to decimals. Third, each series was demeaned using its full-sample arithmetic mean. Standard tests confirmed stationarity for the resulting return series, fulfilling a necessary condition for the modeling framework. The final input data for the DFSV model consist of these $N=95$ demeaned, decimal monthly value-weighted returns, denoted $\vr_t'$. Appendix~\ref{app:detailed_return_stats} contains detailed statistics and plots.

These demeaned return series ($\vr_t'$) exhibit characteristics typical of financial data: means are effectively zero, standard deviations vary across portfolios, and many series display negative skewness and significant excess kurtosis (leptokurtosis). Furthermore, the strong positive correlations observed (Figure~\ref{fig:corr_heatmap_returns}) and well-documented phenomena like volatility clustering and time-varying correlations \parencite{cont_empirical_2001} motivate using models capable of capturing time-varying co-movement, such as the DFSV framework. Appendix~\ref{app:detailed_return_stats}, Table~\ref{tab:summary_stats_returns_concise_appendix}, provides detailed summary statistics.


\begin{figure}[htb]
  \centering
  \caption{Correlation Heatmap of 95 Demeaned Portfolio Returns ($\vr_t'$)}. Five portfolios ('BIG HiBM', 'ME10 BM8', 'ME10 BM9', 'ME7 BM10', 'ME9 BM10') are excluded due to missing values.
  \label{fig:corr_heatmap_returns}
  \includegraphics[width=0.7\textwidth]{eda/figure2_avg_correlation_heatmap.png}
\end{figure}

Figure~\ref{fig:corr_heatmap_returns} shows the sample correlation matrix, revealing strong positive correlations across most portfolio pairs. This observation supports using a factor model to capture common variation efficiently.


\subsection{Principal Component Analysis (PCA) \& Factor Selection ($K$)}\label{sec:pca_factor_selection}

Principal Component Analysis (PCA) on the standardized demeaned returns ($\vr_t'$) helps assess the dimensionality of common variation within the $N=95$ portfolio returns. Standardization ensures that portfolios with higher variance do not disproportionately influence the principal components.

\begin{figure}[htb]
  \centering
  \caption{PCA Scree Plot: Variance Explained by Principal Components}
  \label{fig:pca_scree}
  \includegraphics[width=0.7\textwidth]{eda/figure5_pca_scree_plot.png}
\end{figure}

The PCA scree plot (Figure~\ref{fig:pca_scree}) illustrates the variance explained by successive principal components. The first three components capture approximately 83\% of the total variance, while the first five explain around 85\%, indicating diminishing explanatory power beyond the initial few components.

Although Principal Component Analysis suggests that $K=3$ factors capture the majority of common variation (approximately 83\%, see Figure~\ref{fig:pca_scree}), we select $K=5$ factors for the empirical application. This choice serves multiple purposes. First, it facilitates a more comprehensive evaluation of the Bellman Information Filter's (BIF) performance within a higher-dimensional latent state space ($2K=10$). Second, while the factors in this study are latent and estimated via the DFSV model, a five-factor structure offers a conceptual parallel to established asset pricing frameworks, such as the Fama-French five-factor model \cite{fama_five-factor_2015}. Finally, preliminary assessments confirmed that estimating the DFSV model with $N=95$ assets and $K=5$ factors using the BIF remains computationally feasible given the project's resources.

\section{Benchmark Models}\label{sec:benchmark_models}

To evaluate the proposed DFSV-BIF model, its performance is compared against three benchmark models. Section~\ref{sec:methodology:benchmark_models} provides methodological details; the specific configurations used for the empirical application ($N=95$ assets, $K=5$ factors where applicable) are:
\begin{itemize}
    \item \textbf{DFSV-PF:} Employs the same DFSV specification as the primary model but is estimated using a Particle Filter (PF) with $P=10,000$ particles (see Sections~\ref{sec:particle_filter} and \ref{sec:simulation_design}). This setup permits a direct comparison of BIF and PF estimation for the identical model structure.
    \item \textbf{DCC-GARCH:} Uses a standard DCC(1,1) specification with GARCH(1,1) for each return series ($\vr_t'$). Estimation uses Maximum Likelihood via the `mvgarch` python library (see Section~\ref{sec:methodology:benchmark:dcc_garch}). This benchmark contrasts the factor approach with direct covariance modeling.
    \item \textbf{DFM:} Represents a Dynamic Factor Model with constant volatility and $K=5$ factors. Estimation uses the Kalman Filter and Maximum Likelihood via the `statsmodels` python library (see Section~\ref{sec:methodology:benchmark:dfm}). This model helps isolate the impact of incorporating stochastic volatility in the DFSV models.
\end{itemize}

\section{Evaluation Framework}\label{sec:evaluation_framework}

With the models, data, and benchmarks established (Chapter~\ref{ch:methodology}, Sections~\ref{sec:empirical_data}, \ref{sec:benchmark_models}), a comprehensive evaluation framework is necessary to assess the DFSV-BIF model's effectiveness in explaining and forecasting high-dimensional returns relative to its competitors over the full sample period. This assessment rests on six pillars: statistical fit, covariance dynamics, factor relevance, residual behavior, parameter/state plausibility, and computational efficiency. The subsequent subsections detail the specific metrics within each pillar, forming the basis for the empirical application presented in Chapter~\ref{ch:empirical_application}.

\subsection{Scope and Estimation Window}\label{sec:evaluation:scope_window}

The evaluation utilizes all $T = 726$ monthly observations from July 1963 to December 2023 (Section~\ref{sec:empirical_data}). Compared models include the DFSV-BIF (Bellman filter), DFSV-PF (particle filter), DFM (Dynamic Factor Model), and DCC-GARCH(1,1). All criteria are calculated in-sample to maximize information for parameter estimation; out-of-sample evaluation remains a topic for future research. Section~\ref{sec:methodology:model_summary} contains the model equations and estimation details.

\subsection{Overview of Evaluation Pillars}\label{sec:evaluation:pillars_overview}

Table~\ref{tab:evaluation_pillars_final} outlines the evaluation framework, detailing the key areas, metrics, and their purpose.

\begin{table}[htbp]
  \centering
  \caption{Evaluation pillars and primary metrics}
  \label{tab:evaluation_pillars_final} % New label for the final table
  \begin{tabularx}{\textwidth}{lXX}
    \toprule
    Pillar & Metric(s) & Purpose \\ % Updated header
    \midrule
    Statistical fit & (Pseudo) LL, AIC, BIC & Assess overall goodness-of-fit, penalizing complexity \\
    Covariance dynamics & Log GV ($\log |\hat{\mSigma}_t|$), Avg. $\hat{\rho}_t$ & Capture systemic variance and market co-movement dynamics driven by factors \\
    Residual behavior & Jarque–Bera, LB-Q² (lags 5,10,15,20), ARCH-LM (lags 5, 10) & Detect unmodeled volatility (e.g., non-normality, serial correlation, ARCH effects), potentially including idiosyncratic dynamics \\
    Parameter \& state plausibility & Stationarity eigenvalues, economic ranges, heatmaps & Perform sanity checks on estimated parameters ($\hat{\mLambda}$, $\hat{\mPhi}_f$, $\hat{\mPhi}_h$, $\hat{\vmu}$) and states ($\hat{\vf}_t, \hat{\vh}_t$) \\
    Computational efficiency & Wall-clock minutes, optimizer iterations, convergence flag & Evaluate practical feasibility and scalability \\
    \bottomrule
  \end{tabularx}
  \par\medskip \footnotesize Note: This table outlines the six key areas for model evaluation, the primary metrics associated with each, their purpose, and the section in Chapter~\ref{ch:empirical_application} where results are presented. LL denotes Log-Likelihood, PLL denotes Pseudo-Log-Likelihood. LB-Q² refers to the Ljung-Box test on squared residuals.
\end{table}

These pillars collectively address RQ-1 (Can the model capture time-varying risk?) and RQ-2 (Is the BIF estimator computationally attractive?), forming the evaluation basis for Chapter~\ref{ch:empirical_application}.

\subsection{Statistical-Fit Criteria}\label{sec:evaluation:stat_fit}

Statistical fit employs the true Log-Likelihood (LL) for DFM and DCC-GARCH, and the Pseudo-Log-Likelihood (PLL) via Laplace approximation for DFSV-BIF and DFSV-PF. Information criteria (AIC, BIC) are calculated based on these likelihoods ($p$ parameters, $T=726$). Direct comparison across likelihood types (LL vs. PLL) is invalid; AIC/BIC comparisons hold only within the same likelihood category.

\subsection{Dynamic-Covariance Metrics}\label{sec:evaluation:dyn_cov}

Dynamic covariance properties are evaluated using the model-implied conditional covariance matrix. For model $m \in \{\text{DFSV-BIF}, \text{DFSV-PF}, \text{DCC}, \text{DFM}\}$, this matrix is defined as:
\begin{equation}
  \widehat{\mSigma}_t^{(m)} := \Var_m (\vr_t \mid \mathcal{F}_{t-1}; \widehat{\mTheta}^{(m)}, \widehat{\valpha}_{t|t}^{(m)}).
  \label{eq:def_cond_cov} % Add a label for potential reference
\end{equation}
This matrix incorporates the final parameter estimates $\widehat{\mTheta}^{(m)}$ and, where applicable, the filtered states $\widehat{\valpha}_{t|t}^{(m)}$. Key metrics derived from $\widehat{\mSigma}_t^{(m)}$ include the Log Generalized Variance ($\log |\hat{\mSigma}_t|$), summarizing total conditional variance, and the average conditional correlation ($\bar{\rho}_t$, the mean off-diagonal of the corresponding correlation matrix), signaling market co-movement. Time-series plots of these metrics allow visual inspection of covariance dynamics.


\subsection{Residual Diagnostics}\label{sec:evaluation:residual_diagnostics}

Standardized residuals $\hat{\vz}_t = \hat{\mSigma}_{t|t-1}^{-1/2} (\vr_t' - \hat{\vr}_{t|t-1}')$ are examined via pass rates (at the 5\% significance level across the 95 series) for the Jarque-Bera test (normality), the Ljung-Box Q² test (lags 5, 10, 15, 20; serial correlation in squared residuals), and the ARCH-LM test (lags 5, 10; remaining ARCH effects).

\subsection{Parameter and State Plausibility Checks}\label{sec:evaluation:plausibility}

Plausibility checks include: verifying stationarity conditions ($|\lambda_{\max}(\hat{\mPhi}_f)| < 1$, $|\lambda_{\max}(\hat{\mPhi}_h)| < 1$); comparing estimated unconditional means ($\hat{\vmu}$) and idiosyncratic variances ($\hat{\mSigma}_\epsilon$) against typical economic ranges; and visually inspecting heatmaps ($\hat{\mLambda}$, $\hat{\mPhi}_f$, $\hat{\mPhi}_h$) and plots of smoothed states $(\hat{\vf}_t, \hat{\vh}_t)$.

\subsection{Computational Cost and Stability}\label{sec:evaluation:comp_cost} % Combined plausibility into table

Assessing computational cost involves recording wall-clock time, optimizer iterations, and convergence status for each estimation run conducted on identical hardware\footnote{Computations performed on a local machine with an Intel Ultra 7 155H processor and 16GB RAM.}. Lower computation time for comparable model fit indicates superior scalability and practical feasibility.

Chapter~\ref{ch:empirical_application} utilizes these six pillars to evaluate whether the Bellman-filtered DFSV model provides a credible and efficient representation of dynamic market risk.

% --- End of Rewritten Section ---

% --- Start of Original Simulation Study Section ---
\section{Simulation Study Design}
\label{sec:simulation_design}

\subsection{Motivation \& Overall Goals}
\label{sec:simulation_design:motivation}
To complement the empirical analysis, this section details two simulation studies designed to systematically evaluate the performance of the Bellman Information Filter (BIF) and Particle Filter (PF) for DFSV models under controlled conditions.
\begin{itemize}
    \item \textbf{Study 1 (Computational Scaling):} This study examines how the computational efficiency (time) and state estimation accuracy (RMSE, correlation) of BIF and PF scale with increasing model dimensions ($N, K$). The goal is assessing the practical feasibility of these methods for high-dimensional applications.
    \item \textbf{Study 2 (Parameter Estimation):} This study evaluates how well BIF and PF recover true model parameters ($\mTheta$) via Maximum Likelihood Estimation (MLE), addressing the critical challenge of parameter estimation in real-world scenarios where true parameters are unknown.
\end{itemize}
Together, these studies provide a comprehensive assessment of the filters' relative strengths and limitations across various dimensions and use cases, informing empirical filter selection and guiding future development. All simulations use identical hardware\footnote{Google Cloud e2-standard-4 instances (4 vCPUs, 16 GB RAM) ensure fair comparisons and allow parallel execution via Google Cloud Batch.} to ensure reproducibility.

\subsection{General Data Generating Process (DGP)}
\label{sec:simulation_design:general_dgp}
Both studies simulate synthetic data from the DFSV model detailed in Chapter~\ref{ch:methodology} (visualized in Figure~\ref{fig:dgp_diagram}). For each replication, model parameters ($\mLambda, \mPhi_f, \mPhi_h, \vmu, \sigma^2_\epsilon, \mQ_h$) are generated randomly, ensuring model stability (e.g., stationarity via eigenvalue constraints) and identifiability. Specific details on parameter generation and configurations for each study appear in their respective sections below and in Appendices~\ref{app:sim1_params} and \ref{app:sim2_params}.

\begin{figure}[htbp]
    \centering
    \resizebox{.9\linewidth}{!}{%
    \begin{tikzpicture}[node distance=1.5cm and 2cm]
      % Top‐level
      \node (start) [startstop]             {Start};
      \node (inp)   [param, right=of start]
        {Input Parameters\\
         $\bm{\Lambda},\,\bm{\Phi}_f,\,\bm{\Phi}_h,\,\bm{\mu},\,\bm{\sigma}^2,\,\bm{Q}_h$\\
         Optional $(\bm{f}_0,\bm{h}_0),\,T$};
      \node (init)  [init,  right=of inp]  {Init Arrays \& States};

      % Loop‐body
      \node (hstep) [updh, below=of init]
        {$\displaystyle \bm{h}_t = \bm{\mu} + \bm{\Phi}_h(\bm{h}_{t-1}-\bm{\mu}) + \bm{\eta}_t$};
      \node (fstep) [updf, below=of hstep]
        {$\displaystyle \bm{f}_t = \bm{\Phi}_f\,\bm{f}_{t-1} + \mathrm{diag}(e^{\bm{h}_t/2})\,\bm{\varepsilon}_t$};
      \node (rstep) [updr, below=of fstep]
        {$\displaystyle \bm{r}_t = \bm{\Lambda}\,\bm{f}_t + \bm{e}_t$};

      % Dashed loop container
      \begin{scope}[on background layer]
        \node[loopcontainer, fit=(hstep)(fstep)(rstep), inner sep=6pt] (loopbox) {};
      \end{scope}
      \node[font=\small, above=of hstep, yshift=-3pt] {Loop for $t=1\ldots T-1$};

      % Output & End
      \node (out) [output, right=of loopbox]
        {Output\\$(\bm{r}_{1:T},\,\bm{f}_{1:T},\,\bm{h}_{1:T})$};
      \node (end) [startstop, right=of out]  {End};

      % Arrows
      \draw[->] (start) -- (inp) -- (init) -- (hstep);
      \draw[->] (hstep) -- (fstep) -- (rstep) -- (out) -- (end);

      % Loop‐back under the box
      \draw[->] (rstep.south)
        .. controls +(-1.0cm,-1.0cm) and +(0,-1.0cm) .. (hstep.south);
    \end{tikzpicture}}
    \caption{Data‑generating process for the Dynamic Factor Stochastic Volatility model.}
    \label{fig:dgp_diagram} % Added label
    \end{figure}

\subsection{Simulation Study 1: Computational Scaling Analysis}
\label{sec:simulation_design:study1_scaling}
Study 1 evaluates BIF and PF computational efficiency and scalability, addressing three key questions: (1) How does filter computation timescale with dimensions ($N, K$)? (2) How does BIF performance compare to PF with various particle counts ($P$)? (3) What are the practical dimensional limits of each filter?

\subsubsection{Data Generating Process (DGP)}
\label{sec:simulation_design:study1_scaling:dgp}
Synthetic data generation follows the general DGP (Section~\ref{sec:simulation_design:general_dgp}) with a time series length of $T = 1500$. The analysis varies model dimensions across assets $N \in \{5, 10, 20, 50, 100, 150\}$ and factors $K \in \{2, 3, 5, 10, 15\}$, subject to the constraint $K < N$. For each $(N, K)$ configuration, 100 replications were generated using unique random seeds. Parameters were generated randomly, ensuring stability and identifiability (details appear in Appendix~\ref{app:sim1_params}, Table~\ref{tab:dgp_parameters_study1_appendix}).

\subsubsection{Filter Configurations}
\label{sec:simulation_design:study1_scaling:filter_config}
The analysis compares the BIF (Section~\ref{sec:bif_update}) against the bootstrap PF (Section~\ref{sec:particle_filter}) using four particle counts: $P \in \{1{,}000, 5{,}000, 10{,}000, 20{,}000\}$. For a controlled comparison focused purely on filtering (not estimation), both filters were initialized with the true initial states $\valpha_1 = [\vf_1', \vh_1']'$ and the true model parameters $\mTheta$. The BIF employs a diagonal initial information matrix (precision 1.0 for factors, 0.1 for log-volatilities), while the PF draws initial particles from a Gaussian centered at $\valpha_1$. Both implementations leverage JAX for computational efficiency.

\subsubsection{Performance Evaluation Metrics}
\label{sec:simulation_design:study1_scaling:metrics}
Performance assessment relies on two categories of metrics:
\begin{itemize}
    \item \textbf{Computational Efficiency:} Total wall-clock time required for filtering the $T=1500$ observations; the analysis examines how computation time scales with $N$, $K$, and $P$.
    \item \textbf{State Estimation Accuracy:} Root Mean Squared Error (RMSE) and Pearson Correlation is calculated between the true simulated states ($\vf_t, \vh_t$) and their filtered estimates ($\hat{\vf}_{t|t}, \hat{\vh}_{t|t}$) at each time step.
\end{itemize}
Results (mean, median, standard error across 100 replications per configuration) are presented using log-scale plots for timing and comparative tables/heatmaps for accuracy metrics to illustrate scaling behavior and trade-offs.

\subsection{Simulation Study 2: Parameter Estimation Performance}
\label{sec:simulation_design:study2_estimation}
Study 2 evaluates the ability of BIF-based and PF-based Maximum Likelihood Estimation (MLE) to recover true DFSV parameters ($\mTheta$) from simulated data. It also assesses the impact of using estimated parameters ($\hat{\mTheta}$) on state filtering accuracy.

\subsubsection{Data Generating Process (DGP)}
\label{sec:simulation_design:study2_estimation:dgp}
Synthetic data generation follows the general DGP (Section~\ref{sec:simulation_design:general_dgp}) but uses configurations relevant to estimation challenges. Time series were simulated for lengths $T \in \{500, 1000, 2000\}$ across nine combinations of assets $N \in \{5, 10, 15\}$ and factors $K \in \{2, 3, 5\}$, plus a larger case ($N=50, K=5$). These configurations cover various asset-to-factor ratios. For each $(N, K, T)$ setting, 5 replications were generated using different random seeds.
Unlike Study 1, which used more general random parameter draws, Study 2 employs specific distributions and imposes identification constraints (e.g., lower-triangular $\mLambda$ with unit diagonal, diagonal $\mQ_h$) necessary for parameter estimation. This process yields realistic simulated series (full details are in Appendix~\ref{app:sim2_params}, Table~\ref{tab:dgp_parameters_study2_appendix}).

\subsubsection{Filter and Estimation Configurations}
\label{sec:simulation_design:study2_estimation:filter_estimation_config}
Maximum Likelihood Estimation (MLE) utilizes two approaches:
\begin{itemize}
    \item BIF-based likelihood approximation, optimized using DampedTrustRegionBFGS. This custom algorithm combines a damped Newton descent strategy with a trust region mechanism to robustly handle the non-convex pseudo-likelihood surface (see Appendix~\ref{app:estimation_algorithm_details}).
    \item PF-based likelihood approximation, optimized using ArmijoBFGS. This custom algorithm combines BFGS with an Armijo line search strategy suitable for the potentially noisy likelihood estimates from the particle filter (see Appendix~\ref{app:estimation_algorithm_details}).
\end{itemize}
The PF approach was tested with $P \in \{1000, 5000\}$ particles (PF-1k, PF-5k). The BIF variant estimates all parameters, including the log-volatility mean $\vmu$. Optimizations were initialized with plausible parameter values, distinct from the true values, simulating a realistic estimation scenario. Appendix~\ref{app:estimation_algorithm_details} provides further details on optimizers, parameter transformations, and constraints.

\subsubsection{Evaluation Themes and Metrics}
\label{sec:simulation_design:study2_estimation:metrics}
Estimation performance evaluation covers five key themes:
\begin{enumerate}
    \item \textbf{Parameter Accuracy:} Assessment of how closely estimated parameters ($\hat{\mTheta}$) match true parameters ($\mTheta$), measured by Root Mean Squared Error (RMSE) and bias for each parameter type ($\mLambda, \mPhi_f, \mPhi_h, \vmu, \sigma^2_\epsilon, \mQ_h$).
    \item \textbf{State Accuracy (Post-Estimation):} Evaluation of the quality of filtered states ($\hat{\vf}_{t|t}, \hat{\vh}_{t|t}$) obtained using the \emph{estimated} parameters $\hat{\mTheta}$, measured by RMSE and correlation against true states.
    \item \textbf{Bias and Identification Difficulty:} Investigation of systematic deviations (bias) of $\hat{\mTheta}$ from $\mTheta$ and identification of parameters that are consistently harder to estimate accurately.
    \item \textbf{Computational Cost vs. Accuracy Trade-offs:} Comparison of estimation efficiency (wall-clock time, iterations) against the resulting parameter and state accuracy.
    \item \textbf{Scaling:} Examination of how estimation performance (accuracy, bias, cost) varies with model dimensions ($N, K$) and time series length ($T$).
\end{enumerate}
Results are reported as means and standard errors across the 5 replications for each configuration, using comparative tables and plots for illustration.
% --- End of Original Simulation Study Section ---
