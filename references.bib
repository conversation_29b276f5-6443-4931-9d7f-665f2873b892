
@misc{rader_optimistix_2024,
	title = {Optimistix: modular optimisation in {JAX} and {Equinox}},
	shorttitle = {Optimistix},
	url = {http://arxiv.org/abs/2402.09983},
	doi = {10.48550/arXiv.2402.09983},
	abstract = {We introduce Optimistix: a nonlinear optimisation library built in JAX and Equinox. Optimistix introduces a novel, modular approach for its minimisers and least-squares solvers. This modularity relies on new practical abstractions for optimisation which we call search and descent, and which generalise classical notions of line search, trust-region, and learning-rate algorithms. It provides high-level APIs and solvers for minimisation, nonlinear least-squares, root-finding, and fixed-point iteration. Optimistix is available at https://github.com/patrick-kidger/optimistix.},
	urldate = {2025-04-27},
	publisher = {arXiv},
	author = {<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
	month = feb,
	year = {2024},
	keywords = {Computer Science - Mathematical Software, Mathematics - Optimization and Control},
}

@article{bai_determining_2002,
	title = {Determining the {Number} of {Factors} in {Approximate} {Factor} {Models}},
	volume = {70},
	copyright = {The Econometric Society 2001},
	issn = {1468-0262},
	url = {https://onlinelibrary.wiley.com/doi/abs/10.1111/1468-0262.00273},
	doi = {10.1111/1468-0262.00273},
	abstract = {In this paper we develop some econometric theory for factor models of large dimensions. The focus is the determination of the number of factors (r), which is an unresolved issue in the rapidly growing literature on multifactor models. We first establish the convergence rate for the factor estimates that will allow for consistent estimation of r. We then propose some panel criteria and show that the number of factors can be consistently estimated using the criteria. The theory is developed under the framework of large cross-sections (N) and large time dimensions (T). No restriction is imposed on the relation between N and T. Simulations show that the proposed criteria have good finite sample properties in many configurations of the panel data encountered in practice.},
	language = {en},
	number = {1},
	urldate = {2025-03-02},
	journal = {Econometrica},
	author = {Bai, Jushan and Ng, Serena},
	year = {2002},
	keywords = {asset pricing, factor analysis, model selection, principal components},
	pages = {191--221},
}

@misc{bradbury_jax_2018,
	title = {{JAX}: composable transformations of {Python}+{NumPy} programs},
	url = {http://github.com/jax-ml/jax},
	author = {Bradbury, James and Frostig, Roy and Hawkins, Peter and Johnson, Matthew James and Leary, Chris and Maclaurin, Dougal and Necula, George and Paszke, Adam and VanderPlas, Jake and Wanderman-Milne, Skye and Zhang, Qiao},
	year = {2018},
}

@article{shumway_approach_1982,
	title = {An {Approach} to {Time} {Series} {Smoothing} and {Forecasting} {Using} the {Em} {Algorithm}},
	volume = {3},
	issn = {1467-9892},
	url = {https://onlinelibrary.wiley.com/doi/abs/10.1111/j.1467-9892.1982.tb00349.x},
	doi = {10.1111/j.1467-9892.1982.tb00349.x},
	abstract = {Abstract. An approach to smoothing and forecasting for time series with missing observations is proposed. For an underlying state-space model, the EM algorithm is used in conjunction with the conventional Kalman smoothed estimators to derive a simple recursive procedure for estimating the parameters by maximum likelihood. An example is given which involves smoothing and forecasting an economic series using the maximum likelihood estimators for the parameters.},
	language = {en},
	number = {4},
	urldate = {2025-03-04},
	journal = {Journal of Time Series Analysis},
	author = {Shumway, R. H. and Stoffer, D. S.},
	year = {1982},
	keywords = {EM algorithm, Kalman filter, Missing data, forecasting, maximum likelihood},
	pages = {253--264},
}

@incollection{BOLLERSLEV19942959,
	series = {Handbook of econometrics},
	title = {Chapter 49 arch models},
	volume = {4},
	url = {https://www.sciencedirect.com/science/article/pii/S1573441205800182},
	abstract = {This chapter evaluates the most important theoretical developments in ARCH type modeling of time-varying conditional variances. The coverage include the specification of univariate parametric ARCH models, general inference procedures, conditions for stationarity and ergodicity, continuous time methods, aggregation and forecasting of ARCH models, multivariate conditional covariance formulations, and the use of model selection criteria in an ARCH context. Additionally, the chapter contains a discussion of the empirical regularities pertaining to the temporal variation in financial market volatility. Motivated in part by recent results on optimal filtering, a new conditional variance model for better characterizing stock return volatility is also presented.},
	publisher = {Elsevier},
	author = {Bollerslev, Tim and Engle, Robert F. and Nelson, Daniel B.},
	year = {1994},
	doi = {https://doi.org/10.1016/S1573-4412(05)80018-2},
	note = {ISSN: 1573-4412},
	pages = {2959--3038},
}

@article{rebeschini_can_2015,
	title = {Can local particle filters beat the curse of dimensionality?},
	volume = {25},
	issn = {1050-5164, 2168-8737},
	doi = {10.1214/14-AAP1061},
	abstract = {The discovery of particle filtering methods has enabled the use of nonlinear filtering in a wide array of applications. Unfortunately, the approximation error of particle filters typically grows exponentially in the dimension of the underlying model. This phenomenon has rendered particle filters of limited use in complex data assimilation problems. In this paper, we argue that it is often possible, at least in principle, to develop local particle filtering algorithms whose approximation error is dimension-free. The key to such developments is the decay of correlations property, which is a spatial counterpart of the much better understood stability property of nonlinear filters. For the simplest possible algorithm of this type, our results provide under suitable assumptions an approximation error bound that is uniform both in time and in the model dimension. More broadly, our results provide a framework for the investigation of filtering problems and algorithms in high dimension.},
	number = {5},
	urldate = {2025-04-24},
	journal = {The Annals of Applied Probability},
	author = {Rebeschini, Patrick and Handel, Ramon van},
	month = oct,
	year = {2015},
	keywords = {60G35, 60K35, 62M20, 65C05, 68Q87, Filtering in high dimension, curse of dimensionality, data assimilation, decay of correlations, filter stability, interacting Markov chains, local particle filters},
	pages = {2809--2866},
}

@techreport{borghi_dynamics_2018,
	type = {{CREATES} {Research} {Paper}},
	title = {The dynamics of factor loadings in the cross-section of returns},
	url = {https://pure.au.dk/portal/en/publications/the-dynamics-of-factor-loadings-in-the-cross-section-of-returns},
	abstract = {In this paper, we propose a two-level factor model with time-varying loadings to investigate the dynamics of factor betas in the cross-section of returns of a large portfolio of 1815  firms from 54 countries over the period 2006-2016. The model contains a global observed financial factor and unobserved global and regional factors consistently estimated via principal component. When unexpected events happen globally, loadings on global factors increase. The dynamics of the global factor loadings is related to the profile of the firm. Loadings persistence is decreasing in firm size and expected returns are increasing in the variance of the loading.},
	institution = {Institut for Økonomi, Aarhus Universitet},
	author = {Borghi, Riccardo and Hillebrand, Eric and Mikkelsen, Jakob and Urga, Giovanni},
	month = dec,
	year = {2018},
	keywords = {High-dimensional factor models, financial, global and regional risk factors, systematic risk, time-varying loadings},
}

@article{philipov_factor_2006,
	title = {Factor {Multivariate} {Stochastic} {Volatility} via {Wishart} {Processes}},
	volume = {25},
	issn = {0747-4938, 1532-4168},
	url = {https://www.tandfonline.com/doi/full/10.1080/07474930600713366},
	doi = {10.1080/07474930600713366},
	language = {en},
	number = {2-3},
	urldate = {2025-04-30},
	journal = {Econometric Reviews},
	author = {Philipov, Alexander and Glickman, Mark E.},
	month = sep,
	year = {2006},
	pages = {311--334},
}

@article{bernanke_measuring_2005,
	title = {Measuring the {Effects} of {Monetary} {Policy}: {A} {Factor}-{Augmented} {Vector} {Autoregressive} ({FAVAR}) {Approach}},
	volume = {120},
	issn = {0033-5533},
	shorttitle = {Measuring the {Effects} of {Monetary} {Policy}},
	url = {https://www.jstor.org/stable/********},
	doi = {10.1162/****************},
	abstract = {Structural vector autoregressions (VARs) are widely used to trace out the effect of monetary policy innovations on the economy. However, the sparse information sets typically used in these empirical models lead to at least three potential problems with the results. First, to the extent that central banks and the private sector have information not reflected in the VAR, the measurement of policy innovations is likely to be contaminated. Second, the choice of a specific data series to represent a general economic concept such as "real activity" is often arbitrary to some degree. Third, impulse responses can be observed only for the included variables, which generally constitute only a small subset of the variables that the researcher and policy-maker care about. In this paper we investigate one potential solution to this limited information problem, which combines the standard structural VAR analysis with recent developments in factor analysis for large data sets. We find that the information that our factor-augmented VAR (FAVAR) methodology exploits is indeed important to properly identify the monetary transmission mechanism. Overall, our results provide a comprehensive and coherent picture of the effect of monetary policy on the economy.},
	number = {1},
	urldate = {2025-03-02},
	journal = {The Quarterly Journal of Economics},
	author = {Bernanke, Ben S. and Boivin, Jean and Eliasz, Piotr},
	year = {2005},
	pages = {387--422},
}

@article{rouah_euler_nodate,
	title = {Euler and {Milstein} {Discretization}},
	language = {en},
	author = {Rouah, Fabrice Douglas},
	keywords = {⛔ No DOI found},
}

@article{mccausland_dynamic_2015,
	title = {{DYNAMIC} {FACTOR} {MODELS} {WITH} {STOCHASTIC} {VOLATILITY}},
	abstract = {I introduce posterior simulation methods for a dynamic latent factor model featuring both mean and variance factors. The cross-sectional dimension may be large, so the methods are applicable to data-rich environments. I apply the new methods in two empirical applications. The ﬁrst involves a panel of 134 real activity and ﬁnancial indicators observed monthly from 1959 to 2015; the second, a panel of 10 currencies, with daily log returns observed over a decade.},
	language = {en},
	author = {Mccausland, William J},
	year = {2015},
	keywords = {⛔ No DOI found},
}

@article{kim_stochastic_1998,
	title = {Stochastic {Volatility}: {Likelihood} {Inference} and {Comparison} with {ARCH} {Models}},
	volume = {65},
	issn = {0034-6527},
	shorttitle = {Stochastic {Volatility}},
	url = {https://www.jstor.org/stable/2566931},
	doi = {10.1111/1467-937X.00050},
	abstract = {In this paper, Markov chain Monte Carlo sampling methods are exploited to provide a unified, practical likelihood-based framework for the analysis of stochastic volatility models. A highly effective method is developed that samples all the unobserved volatilities at once using an approximating offset mixture model, followed by an importance reweighting procedure. This approach is compared with several alternative methods using real data. The paper also develops simulation-based methods for filtering, likelihood evaluation and model failure diagnostics. The issue of model choice using non-nested likelihood ratios and Bayes factors is also investigated. These methods are used to compare the fit of stochastic volatility and GARCH models. All the procedures are illustrated in detail.},
	number = {3},
	urldate = {2025-03-04},
	journal = {The Review of Economic Studies},
	author = {Kim, Sangjoon and Shephard, Neil and Chib, Siddhartha},
	year = {1998},
	pages = {361--393},
}

@article{han_asset_2006,
	title = {Asset {Allocation} with a {High} {Dimensional} {Latent} {Factor} {Stochastic} {Volatility} {Model}},
	volume = {19},
	issn = {0893-9454},
	url = {https://www.jstor.org/stable/3598036},
	doi = {10.1093/rfs/hhj002},
	abstract = {We investigate the implications of time-varying expected return and volatility on asset allocation in a high dimensional setting. We propose a dynamic factor multivariate stochastic volatility (DFMSV) model that allows the first two moments of returns to vary over time for a large number of assets. We then evaluate the economic significance of the DFMSV model by examining the performance of various dynamic portfolio strategies chosen by mean-variance investors in a universe of 36 stocks. We find that the DFMSV dynamic strategies significantly outperform various bench-mark strategies out of sample. This outperformance is robust to different performance measures, investor's objective functions, time periods, and assets.},
	number = {1},
	urldate = {2025-02-28},
	journal = {The Review of Financial Studies},
	author = {Han, Yufeng},
	year = {2006},
	pages = {237--271},
}

@article{malikModellingStochasticVolatility2011,
	title = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}: {A} {Simulated} {Maximum} {Likelihood} {Approach} via {Particle} {Filtering}},
	issn = {1556-5068},
	shorttitle = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}},
	url = {http://www.ssrn.com/abstract=1763783},
	doi = {10.2139/ssrn.1763783},
	language = {en},
	urldate = {2025-04-30},
	journal = {SSRN Electronic Journal},
	author = {Malik, Sheheryar and Pitt, Michael K.},
	year = {2011},
}

@misc{malik_modelling_2011,
	address = {Rochester, NY},
	type = {{SSRN} {Scholarly} {Paper}},
	title = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}: {A} {Simulated} {Maximum} {Likelihood} {Approach} via {Particle} {Filtering}},
	shorttitle = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}},
	url = {https://papers.ssrn.com/abstract=1763783},
	doi = {10.2139/ssrn.1763783},
	abstract = {In this paper we provide a unified methodology for conducting likelihood-based inference on the unknown parameters of a general class of discrete-time stochastic volatility (SV) models, characterized by both a leverage effect and jumps in returns. Given the nonlinear/non-Gaussian state-space form, approximating the likelihood for the parameters is conducted with output generated by the particle filter. Methods are employed to ensure that the approximating likelihood is continuous as a function of the unknown parameters thus enabling the use of standard Newton-Raphson type maximization algorithms. Our approach is robust and efficient relative to alternative Markov Chain Monte Carlo schemes employed in such contexts. In addition it provides a feasible basis for undertaking the nontrivial task of model comparison. Furthermore, we introduce new volatility model, namely SV-GARCH which attempts to bridge the gap between GARCH and stochastic volatility specifications. In nesting the standard GARCH model as a special case, it has the attractive feature of inheriting the same unconditional properties of the standard GARCH model but being conditionally heavier-tailed; thus more robust to outliers. It is demonstrated how this model can be estimated using the described methodology. The technique is applied to daily returns data for S\&P 500 stock price index for various spans. In assessing the relative performance of SV with leverage and jumps and nested specifications, we find strong evidence in favour of a including leverage effect and jumps when modelling stochastic volatility. Additionally, we find very encouraging results for SV-GARCH in terms of predictive ability which is comparable to the other models considered.},
	language = {en},
	urldate = {2025-04-30},
	publisher = {Social Science Research Network},
	author = {Malik, Sheheryar and Pitt, Michael K.},
	month = feb,
	year = {2011},
	keywords = {Jumps, Leverage Effect, Particle Filter, Simulation, State Space, Stochastic Volatility},
}

@phdthesis{dunne_volatility_2021,
	title = {Volatility {Takes} the {Lead} {A} {New} {Approach} to {Stochastic} {Volatility} {Models}},
	url = {https://thesis.eur.nl/pub/59433},
	language = {en},
	urldate = {2024-04-11},
	school = {Erasmus University Rotterdam},
	author = {Dunne, M. J. J. van},
	month = aug,
	year = {2021},
	keywords = {MSc Thesis},
}

@phdthesis{kardux_bivariate_2022,
	title = {Bivariate {Stochastic} {Volatility} {Model} with {General} {Leverage} and {Cross}-{Sectional} {Correlations} {Specification}},
	url = {https://thesis.eur.nl/pub/60852},
	language = {en},
	urldate = {2024-04-11},
	school = {Erasmus University Rotterdam},
	author = {Kardux, E.},
	month = jan,
	year = {2022},
	keywords = {MSc Thesis},
}

@misc{noauthor_kenneth_nodate,
	title = {Kenneth {R}. {French} - {Data} {Library}},
	url = {https://mba.tuck.dartmouth.edu/pages/faculty/ken.french/data_library.html},
	urldate = {2025-03-03},
}

@article{yu_multivariate_2006,
	title = {Multivariate {Stochastic} {Volatility} {Models}: {Bayesian} {Estimation} and {Model} {Comparison}},
	volume = {25},
	issn = {0747-4938},
	shorttitle = {Multivariate {Stochastic} {Volatility} {Models}},
	url = {https://doi.org/10.1080/07474930600713465},
	doi = {10.1080/07474930600713465},
	abstract = {In this paper we show that fully likelihood-based estimation and comparison of multivariate stochastic volatility (SV) models can be easily performed via a freely available Bayesian software called WinBUGS. Moreover, we introduce to the literature several new specifications that are natural extensions to certain existing models, one of which allows for time-varying correlation coefficients. Ideas are illustrated by fitting, to a bivariate time series data of weekly exchange rates, nine multivariate SV models, including the specifications with Granger causality in volatility, time-varying correlations, heavy-tailed error distributions, additive factor structure, and multiplicative factor structure. Empirical results suggest that the best specifications are those that allow for time-varying correlation coefficients.},
	number = {2-3},
	urldate = {2025-03-04},
	journal = {Econometric Reviews},
	author = {Yu, Jun and Meyer, Renate},
	month = sep,
	year = {2006},
	keywords = {C11, C15, C30, DIC, Factors, G12, Granger causality in volatility, Heavy-tailed distributions, MCMC, Multivariate stochastic volatility, Time-varying correlations},
	pages = {361--384},
}

@article{koopman_numerically_2015,
	title = {Numerically {Accelerated} {Importance} {Sampling} for {Nonlinear} {Non}-{Gaussian} {State}-{Space} {Models}},
	volume = {33},
	issn = {0735-0015},
	url = {https://doi.org/10.1080/07350015.2014.925807},
	doi = {10.1080/07350015.2014.925807},
	abstract = {We propose a general likelihood evaluation method for nonlinear non-Gaussian state-space models using the simulation-based method of efficient importance sampling. We minimize the simulation effort by replacing some key steps of the likelihood estimation procedure by numerical integration. We refer to this method as numerically accelerated importance sampling. We show that the likelihood function for models with a high-dimensional state vector and a low-dimensional signal can be evaluated more efficiently using the new method. We report many efficiency gains in an extensive Monte Carlo study as well as in an empirical application using a stochastic volatility model for U.S. stock returns with multiple volatility factors. Supplementary materials for this article are available online.},
	number = {1},
	urldate = {2025-03-04},
	journal = {Journal of Business \& Economic Statistics},
	author = {Koopman, Siem Jan and Lucas, André and Scharth, Marcel},
	month = jan,
	year = {2015},
	keywords = {Control variables, Efficient importance sampling, Kalman filter, Numerical integration, Simulated maximum likelihood, Simulation smoothing, Stochastic volatility model},
	pages = {114--127},
}

@article{kastner_efficient_2017,
	title = {Efficient {Bayesian} {Inference} for {Multivariate} {Factor} {Stochastic} {Volatility} {Models}},
	volume = {26},
	issn = {1061-8600},
	url = {https://doi.org/10.1080/10618600.2017.1322091},
	doi = {10.1080/10618600.2017.1322091},
	abstract = {We discuss efficient Bayesian estimation of dynamic covariance matrices in multivariate time series through a factor stochastic volatility model. In particular, we propose two interweaving strategies to substantially accelerate convergence and mixing of standard MCMC approaches. Similar to marginal data augmentation techniques, the proposed acceleration procedures exploit nonidentifiability issues which frequently arise in factor models. Our new interweaving strategies are easy to implement and come at almost no extra computational cost; nevertheless, they can boost estimation efficiency by several orders of magnitude as is shown in extensive simulation studies. To conclude, the application of our algorithm to a 26-dimensional exchange rate dataset illustrates the superior performance of the new approach for real-world data. Supplementary materials for this article are available online.},
	number = {4},
	urldate = {2025-03-04},
	journal = {Journal of Computational and Graphical Statistics},
	author = {Kastner, Gregor and Frühwirth-Schnatter, Sylvia and Lopes, Hedibert Freitas},
	month = oct,
	year = {2017},
	keywords = {Ancillarity-sufficiency interweaving strategy (ASIS), Curse of dimensionality, Data augmentation, Dynamic correlation, Dynamic covariance, Exchange rate data, Markov chain Monte Carlo (MCMC)},
	pages = {905--917},
}

@article{gordon_novel_1993,
	title = {Novel approach to nonlinear/non-{Gaussian} {Bayesian} state estimation},
	volume = {140},
	url = {https://digital-library.theiet.org/doi/abs/10.1049/ip-f-2.1993.0015},
	doi = {10.1049/ip-f-2.1993.0015},
	abstract = {An algorithm, the bootstrap filter, is proposed for implementing recursive Bayesian filters. The required density of the state vector is represented as a set of random samples, which are updated and propagated by the algorithm. The method is not restricted by assumptions of linearity or Gaussian noise: it may be applied to any state transition or measurement model. A simulation example of the bearings only tracking problem is presented. This simulation includes schemes for improving the efficiency of the basic algorithm. For this example, the performance of the bootstrap filter is greatly superior to the standard extended Kalman filter.},
	number = {2},
	urldate = {2025-04-23},
	journal = {IEE Proceedings F (Radar and Signal Processing)},
	author = {Gordon, N.J. and Salmond, D.J. and Smith, A.F.M.},
	month = apr,
	year = {1993},
	pages = {107--113},
}

@article{cont_empirical_2001,
	title = {Empirical properties of asset returns: stylized facts and statistical issues},
	volume = {1},
	issn = {1469-7688},
	shorttitle = {Empirical properties of asset returns},
	url = {https://doi.org/10.1080/713665670},
	doi = {10.1080/713665670},
	abstract = {We present a set of stylized empirical facts emerging from the statistical analysis of price variations in various types of financial markets. We first discuss some general issues common to all statistical studies of financial time series. Various statistical properties of asset returns are then described: distributional properties, tail properties and extreme fluctuations, pathwise regularity, linear and nonlinear dependence of returns in time and across stocks. Our description emphasizes properties common to a wide variety of markets and instruments. We then show how these statistical properties invalidate many of the common statistical approaches used to study financial data sets and examine some of the statistical problems encountered in each case.},
	number = {2},
	urldate = {2025-04-24},
	journal = {Quantitative Finance},
	author = {Cont, R.},
	month = feb,
	year = {2001},
	pages = {223--236},
}

@article{asai_multivariate_2006,
	title = {Multivariate {Stochastic} {Volatility}: {A} {Review}},
	volume = {25},
	issn = {0747-4938},
	shorttitle = {Multivariate {Stochastic} {Volatility}},
	url = {https://doi.org/10.1080/07474930600713564},
	doi = {10.1080/07474930600713564},
	abstract = {The literature on multivariate stochastic volatility (MSV) models has developed significantly over the last few years. This paper reviews the substantial literature on specification, estimation, and evaluation of MSV models. A wide range of MSV models is presented according to various categories, namely, (i) asymmetric models, (ii) factor models, (iii) time-varying correlation models, and (iv) alternative MSV specifications, including models based on the matrix exponential transformation, the Cholesky decomposition, and the Wishart autoregressive process. Alternative methods of estimation, including quasi-maximum likelihood, simulated maximum likelihood, and Markov chain Monte Carlo methods, are discussed and compared. Various methods of diagnostic checking and model comparison are also reviewed.},
	number = {2-3},
	urldate = {2025-03-04},
	journal = {Econometric Reviews},
	author = {Asai, Manabu and McAleer, Michael and Yu, Jun},
	month = sep,
	year = {2006},
	keywords = {Asymmetry, C11, C15, C32, Diagnostic checking, Estimation, Factor models, Leverage, Model comparison, Multivariate stochastic volatility, Thresholds, Time-varying correlations, Transformations, vG12},
	pages = {145--175},
}

@article{aguilar_bayesian_2000,
	title = {Bayesian {Dynamic} {Factor} {Models} and {Portfolio} {Allocation}},
	volume = {18},
	issn = {0735-0015},
	url = {https://www.jstor.org/stable/1392266},
	doi = {10.2307/1392266},
	abstract = {We discuss the development of dynamic factor models for multivariate financial time series, and the incorporation of stochastic volatility components for latent factor processes. Bayesian inference and computation is developed and explored in a study of the dynamic factor structure of daily spot exchange rates for a selection of international currencies. The models are direct generalizations of univariate stochastic volatility models and represent specific varieties of models recently discussed in the growing multivariate stochastic volatility literature. We discuss model fitting based on retrospective data and sequential analysis for forward filtering and short-term forecasting. Analyses are compared with results from the much simpler method of dynamic variance-matrix discounting that, for over a decade, has been a standard approach in applied financial econometrics. We study these models in analysis, forecasting, and sequential portfolio allocation for a selected set of international exchange-rate-return time series. Our goals are to understand a range of modeling questions arising in using these factor models and to explore empirical performance in portfolio construction relative to discount approaches. We report on our experiences and conclude with comments about the practical utility of structured factor models and on future potential model extensions.},
	number = {3},
	urldate = {2025-03-04},
	journal = {Journal of Business \& Economic Statistics},
	author = {Aguilar, Omar and West, Mike},
	year = {2000},
	pages = {338--357},
}

@article{engle_dynamic_2002,
	title = {Dynamic {Conditional} {Correlation}: {A} {Simple} {Class} of {Multivariate} {Generalized} {Autoregressive} {Conditional} {Heteroskedasticity} {Models}},
	volume = {20},
	issn = {0735-0015, 1537-2707},
	shorttitle = {Dynamic {Conditional} {Correlation}},
	url = {http://www.tandfonline.com/doi/abs/10.1198/073500102288618487},
	doi = {10.1198/073500102288618487},
	language = {en},
	number = {3},
	urldate = {2025-04-30},
	journal = {Journal of Business \& Economic Statistics},
	author = {Engle, Robert},
	month = jul,
	year = {2002},
	pages = {339--350},
}

@article{fama_five-factor_2015,
	title = {A five-factor asset pricing model},
	volume = {116},
	issn = {0304-405X},
	url = {https://www.sciencedirect.com/science/article/pii/S0304405X14002323},
	doi = {10.1016/j.jfineco.2014.10.010},
	abstract = {A five-factor model directed at capturing the size, value, profitability, and investment patterns in average stock returns performs better than the three-factor model of Fama and French (FF, 1993). The five-factor model׳s main problem is its failure to capture the low average returns on small stocks whose returns behave like those of firms that invest a lot despite low profitability. The model׳s performance is not sensitive to the way its factors are defined. With the addition of profitability and investment factors, the value factor of the FF three-factor model becomes redundant for describing average returns in the sample we examine.},
	number = {1},
	urldate = {2025-04-26},
	journal = {Journal of Financial Economics},
	author = {Fama, Eugene F. and French, Kenneth R.},
	month = apr,
	year = {2015},
	keywords = {Asset pricing model, Dividend discount model, Factor model, Investment, Profitability},
	pages = {1--22},
}

@article{fama_common_1993,
	title = {Common risk factors in the returns on stocks and bonds},
	volume = {33},
	issn = {0304-405X},
	url = {https://www.sciencedirect.com/science/article/pii/0304405X93900235},
	doi = {10.1016/0304-405X(93)90023-5},
	abstract = {This paper identifies five common risk factors in the returns on stocks and bonds. There are three stock-market factors: an overall market factor and factors related to firm size and book-to-market equity. There are two bond-market factors, related to maturity and default risks. Stock returns have shared variation due to the stock-market factors, and they are linked to bond returns through shared variation in the bond-market factors. Except for low-grade corporates, the bond-market factors capture the common variation in bond returns. Most important, the five factors seem to explain average returns on stocks and bonds.},
	number = {1},
	urldate = {2025-04-26},
	journal = {Journal of Financial Economics},
	author = {Fama, Eugene F. and French, Kenneth R.},
	month = feb,
	year = {1993},
	pages = {3--56},
}

@misc{malik_modelling_2011-1,
	address = {Rochester, NY},
	type = {{SSRN} {Scholarly} {Paper}},
	title = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}: {A} {Simulated} {Maximum} {Likelihood} {Approach} via {Particle} {Filtering}},
	shorttitle = {Modelling {Stochastic} {Volatility} with {Leverage} and {Jumps}},
	url = {https://papers.ssrn.com/abstract=1763783},
	doi = {10.2139/ssrn.1763783},
	abstract = {In this paper we provide a unified methodology for conducting likelihood-based inference on the unknown parameters of a general class of discrete-time stochastic volatility (SV) models, characterized by both a leverage effect and jumps in returns. Given the nonlinear/non-Gaussian state-space form, approximating the likelihood for the parameters is conducted with output generated by the particle filter. Methods are employed to ensure that the approximating likelihood is continuous as a function of the unknown parameters thus enabling the use of standard Newton-Raphson type maximization algorithms. Our approach is robust and efficient relative to alternative Markov Chain Monte Carlo schemes employed in such contexts. In addition it provides a feasible basis for undertaking the nontrivial task of model comparison. Furthermore, we introduce new volatility model, namely SV-GARCH which attempts to bridge the gap between GARCH and stochastic volatility specifications. In nesting the standard GARCH model as a special case, it has the attractive feature of inheriting the same unconditional properties of the standard GARCH model but being conditionally heavier-tailed; thus more robust to outliers. It is demonstrated how this model can be estimated using the described methodology. The technique is applied to daily returns data for S\&P 500 stock price index for various spans. In assessing the relative performance of SV with leverage and jumps and nested specifications, we find strong evidence in favour of a including leverage effect and jumps when modelling stochastic volatility. Additionally, we find very encouraging results for SV-GARCH in terms of predictive ability which is comparable to the other models considered.},
	language = {en},
	urldate = {2025-03-04},
	publisher = {Social Science Research Network},
	author = {Malik, Sheheryar and Pitt, Michael K.},
	month = feb,
	year = {2011},
	keywords = {Jumps, Leverage Effect, Particle Filter, Simulation, State Space, Stochastic Volatility},
}

@misc{noauthor_qf_nodate,
	title = {{QF} {Thesis} {Proposal}},
	url = {https://www.overleaf.com/project/66058a0751b27d156c887310},
	abstract = {An online LaTeX editor that’s easy to use. No installation, real-time collaboration, version control, hundreds of LaTeX templates, and more.},
	language = {en},
	urldate = {2025-03-04},
}

@article{noauthor_pdf_2024,
	title = {({PDF}) {Factor} {Multivariate} {Stochastic} {Volatility} via {Wishart} {Processes}},
	url = {https://www.researchgate.net/publication/227612674_Factor_Multivariate_Stochastic_Volatility_via_Wishart_Processes},
	doi = {10.1080/07474930600713366},
	abstract = {PDF {\textbar} This paper proposes a high dimensional factor multivariate stochastic volatility (MSV) model in which factor covariance matrices are driven by... {\textbar} Find, read and cite all the research you need on ResearchGate},
	language = {en},
	urldate = {2025-03-04},
	journal = {ResearchGate},
	month = oct,
	year = {2024},
}

@techreport{jacquier_stochastic_1999,
	type = {{CIRANO} {Working} {Paper}},
	title = {Stochastic {Volatility}: {Univariate} and {Multivariate} {Extensions}},
	shorttitle = {Stochastic {Volatility}},
	url = {https://econpapers.repec.org/paper/circirwor/99s-26.htm},
	abstract = {Stochastic volatility models, aka SVOL, are more difficult to estimate than standard time-varying volatility models (ARCH). Advances in the literature now offer well tested estimators for a basic univariate SVOL model. However, the basic model is too restrictive for many economic and finance applications. The use of the basic model can lead to biased volatility forecasts especially around crucial periods of high volatility. We extend the basic SVOL needs to allow for the leverage effect, through a correlation between observable and variance errors, and fat-tails in the conditional distribution. We develop a Bayesian Markov Chain Monte Carlo algorithm for this extended model. We also provide an algorithm to analyze a multivariate factor SVOL model. The method simultaneously performs finite sample inference and smoothing. We document the performance of the estimator and show why the extensions are warranted. We provide the researcher with a range of model diagnostics, such as the identification of outliers for stochastic volatility models or the assessment of the normality of the conditional distribution. We implement this methodology on a number of univariate financial time series. There is strong evidence of (1) non-normal conditional distributions for most series, and (2) a leverage effect for stock returns. We illustrate the robustness of the results to the choice of the prior distributions. These results have policy implications on decisions based upon prediction of volatility, especially when dealing with tail prediction as in risk management. Les modèles de volatilité stochastique, alias SVOL, sont plus durs à estimer que les modèles traditionnels de type ARCH. La littérature récente offre des estimateurs éprouvés pour un modèle SVOL univarié de base. Ce modèle est trop contraignant pour une utilisation en économie financière. Les prévisions de volatilité qu'il produit peuvent etre biaisées, particulièrement quand la volatilité est élevée. Nous généralisons le modèle de base en y ajoutant des effets de levier par le biais d'une corrélation entre les chocs observables et de variance, et la possibilité de distributions conditionnelles à queues épaisses. Nous développons un algorithme bayésien à chaînes markoviennes de Monte Carlo. Nous développons aussi un algorithme pour l'analyse d'un modèle SVOL multivarié à facteurs. Ces estimateurs permettent une inférence en échantillon fini pour les paramètres et les volatilités. Nous documentons les performances de l'estimateur et montrons que les extensions sont nécessaires. Nous testons la normalité des distributions conditionnelles. Cette méthode est mise en oeuvre sur plusieurs séries financières. Il y a une forte évidence (1) de distributions conditionnelles à queues épaisses, et (2) d'effets de levier pour les actifs financiers. Les résultats sont robustes et ont d'importantes implications sur les décisions fondées sur les prédictions de volatilité, particulièrement pour la gestion de risques.},
	urldate = {2025-03-04},
	institution = {CIRANO},
	author = {Jacquier, Eric and Polson, Nicholas G. and Rossi, Peter},
	month = jul,
	year = {1999},
	keywords = {ARCH, MCMC algorithm, Stochastic volatility, Volatilité stochastique, algorithme MCMC, distributions à queues épaisses, effets de levier, fat-tailed distributions, gestion de risque, leverage effect, risk management},
}

@article{harvey_multivariate_1994,
	title = {Multivariate {Stochastic} {Variance} {Models}},
	volume = {61},
	issn = {0034-6527},
	url = {https://doi.org/10.2307/2297980},
	doi = {10.2307/2297980},
	abstract = {Changes in variance, or volatility, over time can be modelled using the approach based on autoregressive conditional heteroscedasticity (ARCH). However, the generalizations to multivariate series can be difficult to estimate and interpret. Another approach is to model variance as an unobserved stochastic process. Although it is not easy to obtain the exact likelihood function for such stochastic variance models, they tie in closely with developments in finance theory and have certain statistical attractions. This article sets up a multivariate model, discusses its statistical treatment and shows how it can be modified to capture common movements in volatility in a very natural way. The model is then fitted to daily observations on exchange rates.},
	number = {2},
	urldate = {2025-03-04},
	journal = {The Review of Economic Studies},
	author = {Harvey, Andrew and Ruiz, Esther and Shephard, Neil},
	month = apr,
	year = {1994},
	pages = {247--264},
}

@article{kantas_particle_2015,
	title = {On {Particle} {Methods} for {Parameter} {Estimation} in {State}-{Space} {Models}},
	volume = {30},
	issn = {0883-4237, 2168-8745},
	url = {https://projecteuclid.org/journals/statistical-science/volume-30/issue-3/On-Particle-Methods-for-Parameter-Estimation-in-State-Space-Models/10.1214/14-STS511.full},
	doi = {10.1214/14-STS511},
	abstract = {Nonlinear non-Gaussian state-space models are ubiquitous in statistics, econometrics, information engineering and signal processing. Particle methods, also known as Sequential Monte Carlo (SMC) methods, provide reliable numerical approximations to the associated state inference problems. However, in most applications, the state-space model of interest also depends on unknown static parameters that need to be estimated from the data. In this context, standard particle methods fail and it is necessary to rely on more sophisticated algorithms. The aim of this paper is to present a comprehensive review of particle methods that have been proposed to perform static parameter estimation in state-space models. We discuss the advantages and limitations of these methods and illustrate their performance on simple models.},
	number = {3},
	urldate = {2025-03-03},
	journal = {Statistical Science},
	author = {Kantas, Nikolas and Doucet, Arnaud and Singh, Sumeetpal S. and Maciejowski, Jan and Chopin, Nicolas},
	month = aug,
	year = {2015},
	note = {Publisher: Institute of Mathematical Statistics},
	keywords = {Bayesian inference, maximum likelihood inference, particle filtering, sequential Monte Carlo, state-space models},
	pages = {328--351},
}

@misc{noauthor_qf_nodate-1,
	title = {{QF} {Thesis} {Proposal} - {Online} {LaTeX} {Editor} {Overleaf}},
	url = {https://www.overleaf.com/project/66058a0751b27d156c887310/detacher},
	urldate = {2025-03-02},
}

@article{cox_valuation_1976,
	title = {The valuation of options for alternative stochastic processes},
	volume = {3},
	issn = {0304-405X},
	url = {https://www.sciencedirect.com/science/article/pii/0304405X76900234},
	doi = {10.1016/0304-405X(76)90023-4},
	abstract = {This paper examines the structure of option valuation problems and develops a new technique for their solution. It also introduces several jump and diffusion processes which have not been used in previous models. The technique is applied to these processes to find explicit option valuation formulas, and solutions to some previously unsolved problems involving the pricing of securities with payouts and potential bankruptcy.},
	number = {1},
	urldate = {2025-03-02},
	journal = {Journal of Financial Economics},
	author = {Cox, John C. and Ross, Stephen A.},
	month = jan,
	year = {1976},
	pages = {145--166},
}

@article{chib_analysis_2006,
	title = {Analysis of high dimensional multivariate stochastic volatility models},
	volume = {134},
	copyright = {https://www.elsevier.com/tdm/userlicense/1.0/},
	issn = {********},
	url = {https://linkinghub.elsevier.com/retrieve/pii/S********05001478},
	doi = {10.1016/j.jeconom.2005.06.026},
	language = {en},
	number = {2},
	urldate = {2025-03-02},
	journal = {Journal of Econometrics},
	author = {Chib, Siddhartha and Nardari, Federico and Shephard, Neil},
	month = oct,
	year = {2006},
	pages = {341--371},
}

@article{ramadan_maximum_2022,
	title = {Maximum {Likelihood} recursive state estimation using the {Expectation} {Maximization} algorithm},
	volume = {144},
	issn = {0005-1098},
	url = {https://www.sciencedirect.com/science/article/pii/S0005109822003417},
	doi = {10.1016/j.automatica.2022.110482},
	abstract = {A Maximum Likelihood recursive state estimator is derived for non-linear state–space models. The estimator iteratively combines a particle filter to generate the predicted/filtered state densities and the Expectation Maximization algorithm to compute the maximum likelihood filtered state estimate. Algorithms for maximum likelihood state filtering, prediction and smoothing are derived. The convergence properties of these algorithms, which are inherited from the Expectation Maximization algorithm and the particle filter, are examined in two examples. For nonlinear state–space systems with linear measurements and additive Gaussian noises, it is shown that the filtering and prediction algorithms reduce to gradient-free optimization in a form of a fixed-point iteration. It is also shown that, with randomized reinitialization, which is feasible because of the simplicity of the algorithm, these methods are able to converge to the Maximum Likelihood Estimate (MLE) of multimodal, truncated and skewed densities, as well as those of disjoint support.},
	urldate = {2025-02-28},
	journal = {Automatica},
	author = {Ramadan, Mohammad S. and Bitmead, Robert R.},
	month = oct,
	year = {2022},
	keywords = {Expectation Maximization, Maximum Likelihood, Particle filter, State estimation},
	pages = {110482},
}

@article{cui_full_2017,
	title = {Full and fast calibration of the {Heston} stochastic volatility model},
	volume = {263},
	issn = {0377-2217},
	url = {https://www.sciencedirect.com/science/article/pii/S0377221717304460},
	doi = {10.1016/j.ejor.2017.05.018},
	abstract = {This paper presents an algorithm for a complete and efficient calibration of the Heston stochastic volatility model. We express the calibration as a nonlinear least-squares problem. We exploit a suitable representation of the Heston characteristic function and modify it to avoid discontinuities caused by branch switchings of complex functions. Using this representation, we obtain the analytical gradient of the price of a vanilla option with respect to the model parameters, which is the key element of all variants of the objective function. The interdependence between the components of the gradient enables an efficient implementation which is around ten times faster than with a numerical gradient. We choose the Levenberg–Marquardt method to calibrate the model and do not observe multiple local minima reported in previous research. Two-dimensional sections show that the objective function is shaped as a narrow valley with a flat bottom. Our method is the fastest calibration of the Heston model developed so far and meets the speed requirement of practical trading.},
	number = {2},
	urldate = {2024-05-01},
	journal = {European Journal of Operational Research},
	author = {Cui, Yiran and del Baño Rollin, Sebastian and Germano, Guido},
	month = dec,
	year = {2017},
	keywords = {Heston model, Levenberg–Marquardt method, Model calibration, Optimisation, Pricing},
	pages = {625--638},
}

@article{zheng_estimation_2011,
	title = {On the estimation of integrated covariance matrices of high dimensional diffusion processes},
	volume = {39},
	issn = {0090-5364},
	url = {http://arxiv.org/abs/1005.1862},
	doi = {10.1214/11-AOS939},
	abstract = {We consider the estimation of integrated covariance (ICV) matrices of high dimensional diffusion processes based on high frequency observations. We start by studying the most commonly used estimator, the realized covariance (RCV) matrix. We show that in the high dimensional case when the dimension \$p\$ and the observation frequency \$n\$ grow in the same rate, the limiting spectral distribution (LSD) of RCV depends on the covolatility process not only through the targeting ICV, but also on how the covolatility process varies in time. We establish a Mar{\textbackslash}v\{c\}enko--Pastur type theorem for weighted sample covariance matrices, based on which we obtain a Mar{\textbackslash}v\{c\}enko--Pastur type theorem for RCV for a class \${\textbackslash}mathcal\{C\}\$ of diffusion processes. The results explicitly demonstrate how the time variability of the covolatility process affects the LSD of RCV. We further propose an alternative estimator, the time-variation adjusted realized covariance (TVARCV) matrix. We show that for processes in class \${\textbackslash}mathcal \{C\}\$, the TVARCV possesses the desirable property that its LSD depends solely on that of the targeting ICV through the Mar{\textbackslash}v\{c\}enko--Pastur equation, and hence, in particular, the TVARCV can be used to recover the empirical spectral distribution of the ICV by using existing algorithms.},
	language = {en},
	number = {6},
	urldate = {2024-05-01},
	journal = {The Annals of Statistics},
	author = {Zheng, Xinghua and Li, Yingying},
	month = dec,
	year = {2011},
	note = {arXiv:1005.1862 [math, q-fin, stat]},
	keywords = {Mathematics - Probability, Mathematics - Statistics Theory, Quantitative Finance - Statistical Finance, Statistics - Methodology},
}

@incollection{mikosch_multivariate_2009,
	address = {Berlin, Heidelberg},
	title = {Multivariate {Stochastic} {Volatility}},
	isbn = {978-3-540-71296-1 978-3-540-71297-8},
	url = {https://link.springer.com/10.1007/978-3-540-71297-8_16},
	abstract = {We provide a detailed summary of the large and vibrant emerging literature that deals with the multivariate modeling of conditional volatility of ﬁnancial time series within the framework of stochastic volatility. The developments and achievements in this area represent one of the great success stories of ﬁnancial econometrics. Three broad classes of multivariate stochastic volatility models have emerged: one that is a direct extension of the univariate class of stochastic volatility model, another that is related to the factor models of multivariate analysis and a third that is based on the direct modeling of time-varying correlation matrices via matrix exponential transformations, Wishart processes and other means. We discuss each of the various model formulations, provide connections and diﬀerences and show how the models are estimated. Given the interest in this area, further significant developments can be expected, perhaps fostered by the overview and details delineated in this paper, especially in the ﬁtting of high-dimensional models.},
	language = {en},
	urldate = {2024-04-05},
	booktitle = {Handbook of {Financial} {Time} {Series}},
	publisher = {Springer Berlin Heidelberg},
	author = {Chib, Siddhartha and Omori, Yasuhiro and Asai, Manabu},
	editor = {Mikosch, Thomas and Kreiß, Jens-Peter and Davis, Richard A. and Andersen, Torben Gustav},
	year = {2009},
	doi = {10.1007/978-3-540-71297-8_16},
	pages = {365--400},
}

@misc{wang_estimation_2019,
	title = {On the estimation of high-dimensional integrated covariance matrix based on high-frequency data with multiple transactions},
	url = {http://arxiv.org/abs/1908.08670},
	abstract = {Due to the mechanism of recording, the presence of multiple transactions at each recording time becomes a common feature for high-frequency data in financial market. Using random matrix theory, this paper considers the estimation of integrated covariance (ICV) matrices of high-dimensional diffusion processes based on multiple high-frequency observations. We start by studying the estimator, the time-variation adjusted realized covariance (TVA) matrix, proposed in Zheng and Li (2011) without microstructure noise. We show that in the high-dimensional case, for a class C of diffusion processes, the limiting spectral distribution (LSD) of averaged TVA depends not only on that of ICV, but also on the numbers of multiple transactions at each recording time. However, in practice, the observed prices are always contaminated by the market microstructure noise. Thus the limiting behavior of pre-averaging averaged TVA matrices is studied based on the noisy multiple observations. We show that for processes in class C, the pre-averaging averaged TVA has desirable properties that it eliminates the effects of microstructure noise and multiple transactions, and its LSD depends solely on that of the ICV matrix. Further, three types of nonlinear shrinkage estimators of ICV are proposed based on high-frequency noisy multiple observations. Simulation studies support our theoretical results and show the finite sample performance of the proposed estimators. At last, the high-frequency portfolio strategies are evaluated under these estimators in real data analysis.},
	urldate = {2024-04-02},
	publisher = {arXiv},
	author = {Wang, Moming and Xia, Ningning and Zhou, You},
	month = sep,
	year = {2019},
	note = {arXiv:1908.08670 [math, stat]},
	keywords = {Mathematics - Statistics Theory, Statistics - Applications},
}

@article{lange_bellman_2024,
	title = {Bellman filtering and smoothing for state–space models},
	volume = {238},
	issn = {0304-4076},
	url = {https://www.sciencedirect.com/science/article/pii/S********23003482},
	doi = {10.1016/j.jeconom.2023.105632},
	abstract = {This paper presents a new filter for state–space models based on Bellman’s dynamic-programming principle, allowing for nonlinearity, non-Gaussianity and degeneracy in the observation and/or state-transition equations. The resulting Bellman filter is a direct generalisation of the (iterated and extended) Kalman filter, enabling scalability to higher dimensions while remaining computationally inexpensive. It can also be extended to enable smoothing. Under suitable conditions, the Bellman-filtered states are stable over time and contractive towards a region around the true state at every time step. Static (hyper)parameters are estimated by maximising a filter-implied pseudo log-likelihood decomposition. In univariate simulation studies, the Bellman filter performs on par with state-of-the-art simulation-based techniques at a fraction of the computational cost. In two empirical applications, involving up to 150 spatial dimensions or highly degenerate/nonlinear state dynamics, the Bellman filter outperforms competing methods in both accuracy and speed.},
	number = {2},
	urldate = {2024-03-28},
	journal = {Journal of Econometrics},
	author = {Lange, Rutger-Jan},
	month = jan,
	year = {2024},
	keywords = {Dynamic programming, Kalman filter, Particle filter, Posterior mode},
	pages = {105632},
}

@article{gribisch_modeling_2023,
	title = {Modeling realized covariance measures with heterogeneous liquidity: {A} generalized matrix-variate {Wishart} state-space model},
	volume = {235},
	issn = {0304-4076},
	shorttitle = {Modeling realized covariance measures with heterogeneous liquidity},
	url = {https://www.sciencedirect.com/science/article/pii/S********22000392},
	doi = {10.1016/j.jeconom.2022.01.007},
	abstract = {We propose to generalize the Wishart state-space model for realized covariance matrices of asset returns in order to capture complex measurement error structures induced by modern robust and data efficient realized covariance estimators and heterogeneous liquidity across assets. Our model assumes that the latent covariance matrix of the assets is observed through their realized covariance matrix with a Riesz measurement density, which generalizes the Wishart to monotone missing data. The Riesz alleviates the Wishart-implied attenuation of measurement errors and translates into a convenient likelihood factorization which facilitates inference using simple Bayesian MCMC procedures. The state-space approach allows for a flexible description of the covariance dynamics implied by the data and an empirical application shows that the model performs very well in- and out-of-sample.},
	number = {1},
	urldate = {2024-03-28},
	journal = {Journal of Econometrics},
	author = {Gribisch, Bastian and Hartkopf, Jan Patrick},
	month = jul,
	year = {2023},
	keywords = {Bayesian inference, Realized covariance, Riesz distribution, State-space model},
	pages = {43--64},
}

@article{gribisch_factor_2020,
	title = {Factor state–space models for high-dimensional realized covariance matrices of asset returns},
	volume = {55},
	issn = {0927-5398},
	url = {https://www.sciencedirect.com/science/article/pii/S0927539819300684},
	doi = {10.1016/j.jempfin.2019.08.003},
	abstract = {We propose a dynamic factor state–space model for high-dimensional covariance matrices of asset returns. It makes use of observed risk factors and assumes that the latent integrated joint covariance matrix of the assets and the factors is observed through their realized covariance matrix with a Wishart measurement density. For the latent integrated covariance matrix of the assets we impose a strict factor structure allowing for dynamic variation in the covariance matrices of the factors and the residual components as well as in the factor loadings. This factor structure translates into a factorization of the Wishart measurement density which facilitates statistical inference based on simple Bayesian MCMC procedures making the approach scalable w.r.t. the number of assets. An empirical application to realized covariance matrices for 60 NYSE traded stocks using the Fama–French factors and sector-specific factors represented by Exchange Traded Funds (ETFs) shows that the model performs very well in- and out of sample.},
	urldate = {2024-03-28},
	journal = {Journal of Empirical Finance},
	author = {Gribisch, Bastian and Hartkopf, Jan Patrick and Liesenfeld, Roman},
	month = jan,
	year = {2020},
	keywords = {Bayesian inference, Factor model, Realized covariance, State–space model, Wishart distribution},
	pages = {1--20},
}

@book{triantafyllopoulos_bayesian_2021,
	address = {Cham},
	series = {Springer {Texts} in {Statistics}},
	title = {Bayesian {Inference} of {State} {Space} {Models}: {Kalman} {Filtering} and {Beyond}},
	copyright = {https://www.springernature.com/gp/researchers/text-and-data-mining},
	isbn = {978-3-030-76123-3 978-3-030-76124-0},
	shorttitle = {Bayesian {Inference} of {State} {Space} {Models}},
	url = {https://link.springer.com/10.1007/978-3-030-76124-0},
	language = {en},
	urldate = {2024-03-28},
	publisher = {Springer International Publishing},
	author = {Triantafyllopoulos, Kostas},
	year = {2021},
	doi = {10.1007/978-3-030-76124-0},
}
