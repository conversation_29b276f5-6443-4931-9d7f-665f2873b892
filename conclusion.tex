\chapter{Conclusion}
\label{ch:conclusion}

% Chapter Introduction (Drafted for Phase 4)
This concluding chapter synthesizes the research undertaken in this thesis. It begins by summarizing the principal findings in direct response to the research questions posed in Chapter~\ref{sec:introduction}. Subsequently, it discusses the broader implications of these findings, particularly concerning the interplay between model specification, estimation filter choice, and the inherent trade-offs in high-dimensional financial modeling. The chapter then explicitly outlines the main contributions of this work, critically acknowledges its limitations, and proposes concrete directions for future research motivated by the results obtained. Finally, it offers brief concluding remarks on the overall endeavor.

\section{Principal Findings}
\label{sec:conclusion:findings}

This section synthesizes the principal findings of the thesis by directly addressing the four research questions posed in Chapter~\ref{sec:introduction}.

First (RQ 1), the research demonstrates that the Bellman Information Filter (BIF) can be effectively customized for high-dimensional Dynamic Factor Stochastic Volatility (DFSV) models. Key adaptations detailed in Chapter~\ref{ch:methodology} include a Block Coordinate Descent (BCD) optimization for the filter update, the integration of Vector Autoregressive (VAR) dynamics for both latent factors and their log-volatilities, and the use of the Expected Fisher Information Matrix (E-FIM) for numerically stable information matrix updates. This customized BIF framework achieves a computational complexity of $O(NK^2 + K^3)$, rendering deterministic state and hyperparameter estimation feasible for typical financial applications where the number of assets $N$ greatly exceeds the number of factors $K$.

Second (RQ 2), simulation studies presented in Chapter~\ref{ch:simulation} establish the BIF's comparative advantages and disadvantages against Particle Filter (PF) benchmarks. The BIF consistently provides superior accuracy in estimating latent factor log-volatility states ($\vh_t$), which are key to capturing dynamic co-movement, and recovering related parameters (particularly factor loadings $\mLambda$ and volatility persistence $\mPhi_h$), alongside greater numerical stability and a more favorable bias-variance trade-off. While PFs can achieve marginally better latent factor-state accuracy in lower dimensions, the BIF remains competitive across configurations. Computationally, the BIF scales cubically with $K$ but linearly with $N$, whereas PF runtime depends linearly on $N$ and the particle count $P$; the BIF proves competitive or faster than PFs requiring large particle counts for stability in the dimensions studied.

Third (RQ 3), the empirical application to 95 Fama-French portfolios (Chapter~\ref{ch:empirical_application}) reveals mixed performance for the DFSV-BIF model relative to benchmarks. The model successfully captures aggregate market dynamics and co-movement (systemic variance, correlation) similarly to DCC-GARCH and produces economically plausible parameters, factor loadings, and interpretable latent states related to co-movement, outperforming the DFSV-PF which yielded unstable states empirically. However, the DFSV-BIF fails standard residual diagnostic tests for conditional heteroskedasticity, performing significantly worse than DCC-GARCH and often no better than a constant-volatility DFM, indicating limitations in capturing full asset-specific volatility. Empirically, the BIF estimation was also slower than the PF, contrary to simulation findings, likely due to the complexity of pseudo-likelihood evaluation with large $N$.

Fourth (RQ 4), the diagnostic analysis in Chapter~\ref{ch:empirical_application} strongly indicates that the poor residual performance stems from model misspecification, specifically the assumption of constant, diagonal idiosyncratic error variance ($\mSigma_\epsilon$). Fitting GARCH models to the DFSV-BIF residuals successfully removes the remaining conditional heteroskedasticity, confirming that significant asset-specific volatility dynamics are missed by the factor SV structure alone. This finding highlights a critical limitation of the employed DFSV specification and motivates extensions incorporating time-varying idiosyncratic volatility.


\section{Discussion and Implications}
\label{sec:conclusion:discussion}

The principal findings reveal important implications regarding both the DFSV model specification and the choice of estimation filter. A central empirical puzzle emerges from Chapter~\ref{ch:empirical_application}: the DFSV-BIF model captures aggregate dynamics and co-movement plausibly (Section~\ref{sec:empirical:comparative_dynamics}) and yields interpretable states related to common factors (Section~\ref{sec:empirical:internal_plausibility}), yet fails standard residual diagnostic tests for conditional heteroskedasticity (Section~\ref{sec:empirical:diagnostic_results}). The subsequent GARCH analysis of these residuals (Section~\ref{sec:garch_analysis}) strongly suggests this discrepancy arises from the model's assumption of constant idiosyncratic variance ($\mSigma_\epsilon$), stemming from its impact on capturing asset-specific volatility. This specification, while common \parencite{aguilar_bayesian_2000}, appears insufficient to capture these individual volatility dynamics remaining after accounting for common factor stochastic volatility, directly addressing the limitation highlighted in RQ 4.

This empirical puzzle informs the implications for filter selection. The simulation studies (Chapter~\ref{ch:simulation}) demonstrate the BIF's strengths: superior log-volatility state accuracy (Section~\ref{sec:sim1:state_accuracy}), reliable parameter recovery (especially for $\mLambda$ and $\mPhi_h$, Section~\ref{sec:sim2:param}), and deterministic stability (Section~\ref{sec:sim1:discussion}). Empirically, the BIF produced interpretable latent states, unlike the PF (Section~\ref{sec:empirical:bif_vs_pf}). However, a distinction arises in computational scaling. While Simulation 1 showed the BIF filter step scales favorably (linearly in $N$, cubically in $K$, Section~\ref{sec:sim1:timing}), the full BIF estimation procedure proved slower empirically than the PF in the high-$N$ setting (Section~\ref{sec:empirical:computational_performance}). This suggests the pseudo-likelihood evaluation dominates the empirical runtime. The Particle Filter, conversely, exhibited significant instability and poor state estimation empirically (Section~\ref{sec:empirical:bif_vs_pf}) and requires careful tuning of particle counts to avoid issues like weight degeneracy noted by \textcite{rebeschini_can_2015}. The choice thus involves a trade-off: the BIF offers robustness and superior estimation quality, particularly for volatilities, but its current estimation approach can be slow for large $N$; the PF might offer faster estimation iterations but carries substantial risks regarding stability and the quality of results, echoing challenges with simulation methods in high dimensions \parencite{kastner_efficient_2017}.

The findings underscore the persistent challenge in financial econometrics of balancing model realism against computational tractability, particularly for high-dimensional systems. This research utilizes the BIF, following \textcite{lange_bellman_2024}, to push the tractability frontier for DFSV models, enabling estimation in dimensions ($N=95, K=5$) that challenge traditional methods. However, the empirical results demonstrate that computational feasibility alone is insufficient; model specification limitations, such as the constant $\mSigma_\epsilon$ assumption, can ultimately constrain performance. Effectively modeling complex financial systems requires navigating this trade-off, selecting estimation methods like the BIF that are computationally viable while acknowledging and addressing the simplifying assumptions inherent in tractable model specifications.

\section{Contributions}
\label{sec:conclusion:contributions}

This thesis makes several contributions to the literature on high-dimensional financial time series modeling and state-space estimation:

\begin{enumerate}
    \item \textbf{Methodological Adaptation of BIF for DFSV Models:} I develop and detail a practical framework for applying the Bellman Information Filter to estimate DFSV models with VAR dynamics for both factors and log-volatilities. Key adaptations include a Block Coordinate Descent (BCD) optimization strategy for the filter update (Section~\ref{sec:bif_update}) and the use of the Expected Fisher Information Matrix (E-FIM) for numerically stable information matrix updates (Appendix~\ref{app:fim}). This tailored framework, achieving $O(NK^2 + K^3)$ complexity, renders deterministic state and hyperparameter estimation feasible for typical financial applications where modeling dynamic co-movement is crucial (addressing RQ 1).

    \item \textbf{Comparative Analysis of Estimation Filters:} I provide a rigorous comparative assessment of the BIF against Particle Filter benchmarks and standard models (DCC-GARCH, DFM) through extensive simulation studies (Chapter~\ref{ch:simulation}) and empirical application (Chapter~\ref{ch:empirical_application}). This analysis quantifies the trade-offs regarding computational scaling, numerical stability, state estimation accuracy (particularly the BIF's advantage in log-volatility estimation), and parameter recovery reliability (addressing RQ 2 and RQ 3).

    \item \textbf{Empirical Findings and Diagnostic Insight:} The empirical application to Fama-French portfolios reveals a key puzzle: the DFSV-BIF captures aggregate market dynamics and co-movement plausibly but fails standard residual diagnostics for conditional heteroskedasticity (Section~\ref{sec:empirical:residual_puzzle}). Subsequent GARCH analysis of residuals strongly identifies the model's assumption of constant, diagonal idiosyncratic variance ($\mSigma_\epsilon$) as the primary source of misspecification, strongly identifying the limitation for capturing full asset volatility (Section~\ref{sec:garch_analysis}). This provides crucial diagnostic insight into the limitations of this common DFSV specification (addressing RQ 3 and RQ 4).

    \item \textbf{Comprehensive Evaluation Approach:} I employ a multi-faceted evaluation framework (Section~\ref{sec:evaluation_framework}) encompassing statistical fit, covariance dynamics, factor loading interpretability, residual diagnostics, parameter plausibility, and computational cost. This provides a balanced approach for assessing complex financial models beyond simple statistical fit metrics.
\end{enumerate}

\section{Limitations}
\label{sec:conclusion:limitations}

Despite the contributions, this research is subject to several limitations that define boundaries for the findings and suggest avenues for refinement:

\begin{enumerate}
    \item \textbf{Constant Idiosyncratic Variance ($\mSigma_\epsilon$):} The most significant limitation identified empirically (Section~\ref{sec:garch_analysis}) is the assumption of constant, diagonal idiosyncratic error variance. This simplification, while common \parencite{aguilar_bayesian_2000}, limits the model's ability to capture the full dynamics of individual asset returns. It proved insufficient to capture asset-specific volatility dynamics remaining after accounting for common factor stochastic volatility, directly leading to the poor performance on residual diagnostic tests (Section~\ref{sec:empirical:diagnostic_results}) and representing the primary model specification issue identified in this analysis.

    \item \textbf{Computational Scaling:} While the BIF filter step exhibits favorable scaling ($O(NK^2 + K^3)$, Section~\ref{sec:sim1:timing}), the full estimation procedure based on direct pseudo-likelihood maximization proved computationally demanding in the empirical application with large $N$ (Section~\ref{sec:empirical:computation_comparison}). The cubic scaling in $K$ within the filter step could still become prohibitive for models requiring a very large number of latent factors ($K \gg 15$), but the empirical bottleneck observed here was related to the overall estimation time for large $N$.

    \item \textbf{Gaussian Approximation:} The BIF relies on Gaussian approximations for the filtering distributions (Section~\ref{sec:bif_estimation}). While simulation studies (Chapter~\ref{ch:simulation}) did not indicate major issues for the specified DFSV model, this approximation could introduce bias or inaccuracy if the true underlying distributions exhibit strong non-Gaussian features (e.g., extreme skewness or heavy tails) not captured by the model's SV components. The empirical failure of Jarque-Bera tests (Table~\ref{tab:empirical_resid_pass_rates}) hints at this possibility.

    \item \textbf{Identification Constraint:} The use of a lower-triangular constraint on the factor loading matrix $\mLambda$ to ensure model identification (Section~\ref{sec:identification}) imposes an arbitrary ordering on the factors. Consequently, the estimated factors are identified statistically rather than corresponding directly to pre-defined economic factors, potentially complicating their direct interpretation.

    \item \textbf{Empirical Scope:} The empirical analysis is based on a specific dataset (US Fama-French 95 portfolios) and focuses exclusively on in-sample estimation and evaluation (Chapter~\ref{ch:empirical_application}). The findings regarding model performance and limitations may not generalize directly to other asset classes, markets, or time periods. Furthermore, the lack of out-of-sample forecasting assessment limits conclusions about the model's practical predictive utility.
\end{enumerate}

\section{Future Research Directions}
\label{sec:conclusion:future_research}

The findings and limitations of this thesis motivate several promising directions for future research:

\begin{enumerate}
    \item \textbf{Incorporate Time-Varying Idiosyncratic Volatility:} This is the most critical extension, directly motivated by the empirical diagnostic failures (Section~\ref{sec:garch_analysis}) attributed to the constant $\mSigma_\epsilon$ assumption. Future work should explore tractable ways to introduce dynamics in $\mSigma_{\epsilon,t}$ within the BIF framework. Potential approaches include grouped structures (e.g., common idiosyncratic SV within industries or characteristic portfolios) or computationally efficient univariate SV models for the idiosyncratic terms. This aims to capture the idiosyncratic component of volatility and move towards a more comprehensive model of full asset volatility, building upon the current framework's foundation for modeling co-movement.

    \item \textbf{Explore Alternative Dynamics:} The current model uses VAR(1) processes for factors and log-volatilities. Future research could investigate richer dynamic specifications to capture additional stylized facts. This includes incorporating leverage effects (asymmetric volatility responses) into the factor or volatility processes, exploring non-linear dynamics or regime-switching models to better capture crisis periods observed empirically (Figure~\ref{fig:empirical_factors_bif}), or considering long-memory processes if suggested by data.

    \item \textbf{Employ Non-Gaussian Error Distributions:} Given the universal failure of Jarque-Bera normality tests on residuals (Table~\ref{tab:empirical_resid_pass_rates}), exploring non-Gaussian distributions (e.g., Student's t) for the factor innovations ($\vnu_t$) or log-volatility innovations ($\veta_t$) within the BIF framework could improve model fit and provide more realistic density forecasts.

    \item \textbf{Conduct Out-of-Sample Forecasting Analysis:} Extending the empirical analysis to evaluate the out-of-sample forecasting performance of the DFSV-BIF model for covariance matrices and portfolio risk metrics is essential. Comparing its forecasting accuracy against benchmarks like DCC-GARCH would provide crucial insights into its practical utility for risk management and asset allocation.

    \item \textbf{Investigate Alternative Estimation Strategies (EM Algorithm):} Motivated by the BIF's long empirical estimation runtime when using direct pseudo-likelihood maximization (Section~\ref{sec:empirical:computation_comparison}), exploring alternative strategies like the Expectation-Maximization (EM) algorithm could be fruitful. The BIF, with its efficient filter scaling, could potentially be used effectively within the E-step to compute expected sufficient statistics, possibly offering computational advantages for parameter estimation compared to the direct optimization approach used in this thesis.

    \item \textbf{Apply to Other Asset Classes and Problems:} Testing the generalizability of the DFSV-BIF framework by applying it to different asset classes (e.g., international equities, fixed income, commodities) or different financial problems (e.g., systemic risk measurement, derivative pricing) would further validate its usefulness and potentially reveal new modeling challenges.
\end{enumerate}

% Section 6.6: Concluding Remarks (Drafted for Phase 4)
\section{Concluding Remarks}
\label{sec:conclusion:remarks}

This thesis investigated the application of the Bellman Information Filter to estimate high-dimensional Dynamic Factor Stochastic Volatility models. The research demonstrates that the BIF provides a computationally feasible and numerically stable approach, offering significant advantages in estimating latent factor log-volatility states related to co-movement and recovering key model parameters compared to standard particle filters. However, the empirical analysis highlights a crucial limitation: the standard DFSV specification, even when estimated with the advanced BIF, struggles to capture asset-specific volatility dynamics due to the constant idiosyncratic variance assumption. While successful in modeling co-movement driven by factors, the model falls short of capturing the full volatility picture. Ultimately, this work underscores the ongoing tension between model realism and computational tractability in financial econometrics. While the BIF pushes the boundary of what is computationally feasible for complex factor models focused on co-movement, achieving a truly comprehensive description of high-dimensional financial risk likely requires further innovations in both model specification (particularly regarding idiosyncratic volatility) and estimation techniques to bridge the remaining gap between tractable approximations and market reality.
