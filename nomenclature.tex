% Nomenclature entries for the thesis
% These entries will be processed by the nomencl package

% Abbreviations
\nomenclature[a]{BIF}{Bellman Information Filter}
\nomenclature[a]{BF}{Bellman Filter}
\nomenclature[a]{DFSV}{Dynamic Factor Stochastic Volatility}
\nomenclature[a]{PF}{Particle Filter}
\nomenclature[a]{SISR}{Sequential Importance Sampling with Resampling}
\nomenclature[a]{BCD}{Block Coordinate Descent}
\nomenclature[a]{VAR}{Vector Autoregressive}
\nomenclature[a]{FIM}{Fisher Information Matrix}
\nomenclature[a]{E-FIM}{Expected Fisher Information Matrix}
\nomenclature[a]{O-FIM}{Observed Fisher Information Matrix}
\nomenclature[a]{RMSE}{Root Mean Squared Error}
\nomenclature[a]{DCC}{Dynamic Conditional Correlation}
\nomenclature[a]{DFM}{Dynamic Factor Model}
\nomenclature[a]{GARCH}{Generalized Autoregressive Conditional Heteroskedasticity}
\nomenclature[a]{NBER}{National Bureau of Economic Research}
\nomenclature[a]{EM}{Expectation-Maximization}
\nomenclature[a]{PD}{Positive Definite}
\nomenclature[a]{PSD}{Positive Semi-Definite}
\nomenclature[a]{AIC}{Akaike Information Criterion}

% Latin Symbols - Vectors
\nomenclature[v]{$\vr_t$}{Vector of asset returns at time $t$}
\nomenclature[v]{$\vf_t$}{Vector of latent factors at time $t$}
\nomenclature[v]{$\vh_t$}{Vector of log-volatilities at time $t$}
\nomenclature[v]{$\valpha_t$}{State vector $[\vf_t', \vh_t']'$ at time $t$}
\nomenclature[v]{$\vmu$}{Long-run mean of log-volatilities}
\nomenclature[v]{$\vepsilon_t$}{Idiosyncratic error vector in observation equation}
\nomenclature[v]{$\vnu_{t+1}$}{Innovation vector in factor evolution equation}
\nomenclature[v]{$\veta_{t+1}$}{Innovation vector in log-volatility evolution equation}
\nomenclature[v]{$\vones$}{Vector of ones}
\nomenclature[v]{$\vzeros$}{Vector of zeros}
\nomenclature[v]{$\vz_t$}{Standardized residual vector at time $t$}

% Latin Symbols - Matrices
\nomenclature[m]{$\mLambda$}{Factor loading matrix}
\nomenclature[m]{$\mPhi_f$}{Factor transition matrix}
\nomenclature[m]{$\mPhi_h$}{Log-volatility transition matrix}
\nomenclature[m]{$\mSigma_\epsilon$}{Idiosyncratic error covariance matrix (diagonal)}
\nomenclature[m]{$\mSigma_t$}{Marginal covariance matrix of returns}
\nomenclature[m]{$\mQ_h$}{Log-volatility innovation covariance matrix}
\nomenclature[m]{$\mQ_{t|t-1}$}{State innovation covariance matrix}
\nomenclature[m]{$\mT$}{State transition matrix}
\nomenclature[m]{$\mP_{t|t}$}{Posterior covariance matrix at time $t$}
\nomenclature[m]{$\mP_{t|t-1}$}{Predicted covariance matrix at time $t$}
\nomenclature[m]{$\mOmega_{t|t}$}{Posterior precision (information) matrix at time $t$}
\nomenclature[m]{$\mOmega_{t|t-1}$}{Predicted precision (information) matrix at time $t$}
\nomenclature[m]{$\Infmat$}{Fisher Information Matrix}
\nomenclature[m]{$\mZeros$}{Matrix of zeros}
\nomenclature[m]{$\mR_t$}{Conditional correlation matrix at time $t$}

% Latin Symbols - Scalars
\nomenclature[s]{$N$}{Number of observed series (assets)}
\nomenclature[s]{$K$}{Number of latent factors}
\nomenclature[s]{$T$}{Number of time periods}
\nomenclature[s]{$P$}{Number of particles in Particle Filter}
\nomenclature[s]{$\ell(\cdot)$}{Log-likelihood function}
\nomenclature[s]{$\mathcal{L}(\mTheta)$}{Pseudo-likelihood function for parameter vector $\mTheta$}
\nomenclature[s]{$\bar{\rho}_t$}{Average pairwise conditional correlation at time $t$}

% Greek Symbols
\nomenclature[g]{$\mTheta$}{Parameter vector containing all model parameters}
\nomenclature[g]{$\lambda$}{Penalty coefficient for eigenvalue constraint}
\nomenclature[g]{$\epsilon$}{Small buffer for eigenvalue constraint}
\nomenclature[g]{$\sigma_{i,t}$}{Conditional standard deviation for asset $i$ at time $t$}
\nomenclature[g]{$\rho_{ij,t}$}{Conditional correlation between assets $i$ and $j$ at time $t$}

% Operators and Functions
\nomenclature[o]{$\E[\cdot]$}{Expectation operator}
\nomenclature[o]{$\var(\cdot)$}{Variance operator}
\nomenclature[o]{$\cov(\cdot,\cdot)$}{Covariance operator}
\nomenclature[o]{$\diag(\cdot)$}{Diagonal operator (extracts diagonal or creates diagonal matrix)}
\nomenclature[o]{$\det(\cdot)$}{Determinant of a matrix}
\nomenclature[o]{$\tr(\cdot)$}{Trace of a matrix}
\nomenclature[o]{$\text{softplus}(x)$}{Function $\log(1 + e^x)$ that maps any real number to a positive value}
\nomenclature[o]{$\text{tanh}(x)$}{Hyperbolic tangent function}
\nomenclature[o]{$\mathcal{N}(\mu, \Sigma)$}{Normal distribution with mean $\mu$ and covariance $\Sigma$}
\nomenclature[o]{$\mathcal{F}_t$}{Information set (filtration) up to time $t$}
\nomenclature[o]{$p(\cdot|\cdot)$}{Conditional probability density function}
