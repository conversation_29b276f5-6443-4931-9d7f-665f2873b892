\chapter{Time Frame and Project Timeline}
\begin{table}[h!]
\centering
\caption{Week-by-Week Thesis Roadmap}
\label{tab:thesis_roadmap_7_nolit}
\begin{tabular}{p{3cm} p{12cm}}
\hline
\textbf{Week 1} & 
\textbf{Problem Refinement, Model Structure, and Simulation Setup:} 
Refine the problem statement and finalize the dynamic factor SV model structure (e.g., initial factor count). 
Formally specify the state-space equations, and prepare a simple data-generating process (DGP) for initial testing. 
Develop preliminary code (in Python) for simulating the DGP, and outline the Bellman filter algorithm in pseudo-code.
\\
\hline

\textbf{Week 2} & 
\textbf{Implementing the Bellman Filter Algorithm:} 
Construct the Bellman filter for a simple nonlinear state-space model, starting with a univariate SV example to validate basic functionality. 
Compare results against known methods (e.g., EKF), then extend to handle multiple factors or assets. 
Apply the filter to the small DGP from Week~1, addressing issues related to convergence or optimization. 
By the end of this week, ensure stable state estimates in simplified cases.
\\
\hline

\textbf{Week 3} &
\textbf{Hyperparameter Estimation Module:} 
Develop routines to compute the pseudo-likelihood from the Bellman filter output. 
Implement an optimization approach (e.g., gradient-based or \texttt{scipy} optimizers) to maximize this likelihood. 
Test on simulated data with known parameters to confirm estimation accuracy. 
Address any stability concerns (e.g., adding regularization or handling factor label swapping). 
By the end of this week, have an end-to-end pipeline—filtering plus parameter estimation—on simulated data. 
\\
\hline

\textbf{Week 4} &
\textbf{Empirical Data Preparation:} 
Start documenting results of simulation studies. Obtain Fama–French factor data and sector (or asset) return data. 
Clean and align these datasets to consistent frequencies (e.g., monthly or weekly). 
Perform exploratory data analysis: compute summary statistics, correlation matrices, and possibly run a PCA to guide factor selection. 
Convert data into model-compatible formats, and if time allows, conduct a preliminary run of the Bellman filter on a small subset of real data to identify issues.
\\
\hline

\textbf{Week 5} &
\textbf{Model Application and Tuning on Real Data:} 
Run the Bellman filter and hyperparameter estimation on the Fama–French factor data. 
Monitor convergence, adjusting optimization settings if needed. 
Once stable, apply the same approach to the sector or asset returns. 
Optimize code performance for higher-dimensional data, and document outcomes (e.g., estimated factors, volatilities, log-likelihoods). Start documenting results of empirical studies.
\\
\hline

\textbf{Week 6} &
\textbf{Benchmarking \& Analysis:} 
Compare the proposed model’s performance to benchmarks like DCC-GARCH, EKF, or particle filters. 
Assess model fit (AIC/BIC), forecast accuracy (e.g., RMSE, VaR exceedance), and interpret the results. 
Refine the model or data handling if results are unexpected. 
Perform robustness checks (subperiod estimations, modifications of assumptions), and produce tables and figures. Document benchmark results.
\\
\hline

\textbf{Week 7} &
\textbf{Thesis Writing \& Finalization:} 
Compile methodology, findings, and analyses into the thesis document. 
Integrate all relevant elements: problem context, model details, empirical outcomes, and comparisons. 
Prepare visuals (tables, plots) for clarity. 
Perform final proofreading and ensure references are complete. 
By the end of this week, finalize the thesis, presenting a coherent narrative from problem statement to conclusion.
\\
\hline
\end{tabular}
\end{table}


