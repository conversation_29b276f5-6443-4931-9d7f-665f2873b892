% This is the file econometrics.sty.
% This style file underlies the paper
% "Notation in Econometrics: a proposal for a standard"
% by <PERSON><PERSON> and <PERSON>,
% The Econometrics Journal (2002), 5, 76-90.
%
% Copyright (c) 2016 by <PERSON>.
% All Rights Reserved.
%
% This work may be distributed and/or modified under the
% conditions of the LaTeX Project Public License, either version 1.3
% of this license or (at your option) any later version.
% The latest version of this license is in
%   http://www.latex-project.org/lppl.txt
% and version 1.3 or later is part of all distributions of LaTeX
% version 2005/12/01 or later.
%
% This work has the LPPL maintenance status "maintained".
%
% The Current Maintainer of this work is <PERSON>.
%
% This work consists of the files econometrics.sty, econometrics.tex and
% econometrics.pdf

% This version: 1.0, January 15, 2016
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
\documentclass[12pt, a4paper]{ltxguide}
%\usepackage{a4wide}
\usepackage[margin=2.25cm]{geometry}
\usepackage[english]{babel}
\usepackage[latin1]{inputenc}
\usepackage[fleqn]{amsmath}      % AMS
\usepackage{amssymb}
\usepackage{bm}
\usepackage{econometrics}
\usepackage[authoryear,round]{natbib}
\usepackage{booktabs}
\usepackage{url}

\title{The |econometrics| package}
\author{Erik Kole\thanks{Econometric Institute, Erasmus School of Economics, Erasmus University Rotterdam, Netherlands. Home page: \texttt{http://people.few.eur.nl/kole},  E-mail: \texttt{<EMAIL>}.}}

\date{Version 1.0, January 15, 2016}

\begin{document}
\maketitle


\begin{abstract}
\noindent This package contains commands for notation in econometric writing as proposed by \citet{Abadir02}.
\end{abstract}

\section{Introduction}
\citet{Abadir02} propose standards for mathematical and statistical notation when writing economic or econometric articles. Their proposed standards concern the notation of sets, vectors, matrices and some common operators. The package provides commands based on their proposal. It has also circulated as |ee.sty|.

To use the package, include the command |\usepackage{econometrics}| in the preambule of your document. No general options are available. The package requires the presence of the packages |amsmath|, |amssymb| and |bm|.

\section{Using the package}

\subsection{Typesetting single letters}
The package contains the following commands for typesetting letters to denote mathematical concepts
\begin{itemize}
    \item Sets: |\S| followed by another letter sets the letter in blackboard bold type. \\ Example: |\SN| produces $\SN$ to denote the set of natural numbers. \\ Available for C, N, Q, R, and Z.
    \item Vectors: |\v| followed by another lower case letter sets the letter in bold type. \\ Example: |\va| produces $\va$; |\valpha| produces $\valpha$. \\ Available for all lower case Latin and Greek letters.
    \item Matrices: |m| followed by a capital letter sets the letter in bold type. \\ Example: |\mG| produces $\mG$; |\mGamma| produces $\mGamma$. \\ Available for all upper case Latin letters and the Greek upper case letters that differ from Latin ones.
    \item Calligraphic letters: |\cal| followed by a capital letter sets the letter in calligraphic type. \\ Example: |\calF| produces $\calF$. \\ Available for all upper case Roman letters.
    \item Roman letters: |\r| followed by a letter sets the letter in roman type. \\ Example: |\rb| produces $\rb$; |\rB| produces $\rB$. \\ Available for b, B, C, D, f, F, G, H, L, N, t, U. and W.
\end{itemize}

\subsection{Typesetting constants, statistical distributions and symbols}
The package contains the following commands for typesetting constants, statistical distributions, symbols and special matrices and vectors.

\begin{center}
    \begin{tabular}{lll} \toprule
        command & produces & description \\ \midrule
        |\eu|, |\e| & $\eu$ & Euler's constant \\
        |\iu| & $\iu$ & imaginary unit \\
        |\rGam| & $\rGam$ & Gamma distribution \\
        |\rBeta| & $\rBeta$ & Beta distribution \\
        |\Bin| & $\Bin$ & Binomial distribution \\
        |\LN| & $\LN$ & Lognormal distribution \\
        |\IN| & $\IN$ & sequence of independent normal distributions \\
        |\Poi| & $\Poi$ & Poisson distribution \\
        |\Infmat| & $\Infmat$ & information matrix \\
        |\Hesmat| & $\Hesmat$ & Hessian matrix \\
        |\vones| & $\vones$ & vector with ones \\
        |\vzeros| & $\vzeros$ & vector with zeros \\
        |\mZeros| & $\mZeros$ & matrix with zeros \\
        \bottomrule
    \end{tabular}\\
\end{center}

The commands |\ap| (from apex) and |\ped| (from pedex) both take one argument and typeset it as a superscript or subscript in math-roman. Examples: |$r\ap{e}_t$| produces $r\ap{e}_t$, which is typically used to denote a return at time $t$ in excess of the risk-free rate; |$r\ped{f}$| produce $r\ped{f}$, which is typically used to denote the risk-free rate.


The command |\bcdot| can be used when indicating a row or column from a matrix. Example: row $i$ or column $j$ of a $(m \times n)$ matrix A (with $0 < i \leq m$, $0<j\leq n$) can be referred to as $A_{i\bcdot}$ (|A_{i\bcdot}|) and $A_{\bcdot j}$ (|A_{\bcdot j}|).

\subsection{Common functions and operators}
The operators |\Re| and |\Im| that return the real and imaginary part of a complex number are redefined to produce $\Re$ and $\Im$ in math-roman (instead of the capital fraktur typesetting).

The notation for the differential and partial differential operators can be generated by the commands |\deriv| and |\pderiv| that both take two mandatory arguments (for the numerator and the denominator) and one optional argument for the order. They are defined with the |\frac| command. Examples: |\deriv{f}{x}|, |\deriv[2]{f}{x}| and |\pderiv[n]{y}{x}| produces $\deriv{f}{x}$ and $\pderiv[2]{f}{x}$ $\pderiv[n]{x}{y}$ (inline) and
\begin{equation*}
\deriv{f}{x}, \quad \qquad \deriv[2]{f}{x}, \quad \qquad \pderiv[n]{x}{y},
\end{equation*}
in the |equation| environment.


The following commands are defined by |\operatorname|.

\begin{center}
    \begin{tabular}{lll} \toprule
        command & produces & description \\ \midrule
        |\bias| & $\bias$ & bias (of an estimator) \\
        |\col| & $\col$ & column space (of a matrix) \\
        |\corr| & $\corr$ & correlation \\
        |\cov| & $\cov$ & covariance \\
        |\dg| & $\dg$ & returns the diagonal elements of a matrix \\
        |\diag| & $\diag$ & returns a diagonal matrix with arguments on the diagonal \\
        |\E| & $\E$ & expectation \\
        |\etr| & $\etr$ & exponential of the trace of a matrix \\
        |\ip| & $\ip$ & integer part \\
        |\kur| & $\kur$ & kurtosis \\
        |\MSE| & $\MSE$ & mean squared error \\
        |\MSFE| & $\MSFE$ & mean squared forecasting error \\
        |\OLS| & $\OLS$ & ordinary least squares \\
        |\plim| & $\plim$ & probability limit \\
        |\resid| & $\resid$ & residuals \\
        |\rk| & $\rk$ & matrix rank \\
        |\SE| & $\SE$ & standard error \\
        |\sgn| & $\sgn$ & sign \\
        |\tr| & $\tr$ & trace of a matrix \\
        |\var| & $\var$ & variance \\
        |\vec| & $\vec$ & vectorisation of a matrix \\
        |\vech| & $\vech$ & vectorisation of the lower triangle of a matrix \\ \bottomrule
    \end{tabular}
\end{center}

\subsection{New names for existing commands}
The package introduces some new commands that are equivalent with existing commands, and some derived commands.

\begin{center}
    \begin{tabular}{llll} \toprule
        command & produces & equivalent & description \\ \midrule
        |\distr| & $\distr$ & |\sim| &  distributed as \\
        |\adistr| & $\adistr$ & & asymptotically distributed as \\
        |\diff| & $\diff$ & |\Delta| & difference operator \\
        |\bdiff| & $\bdiff$ & & backward difference operator \\
        |\fdiff| & $\fdiff$ & & forward difference operator \\
        |\eps| & $\eps$ & |\epsilon| & arbitrarily small positive number\\
        |\epsi| & $\epsi$ & |\varepsilon| & disturbance term \\
        |\longto| & $\longto$ & |\longrightarrow| & almost sure convergence to \\
        |\pto| & $\pto$ & & convergence in probability to \\
        |\pto| & $\dto$ & & convergence in distribution to \\
        |\wto| & $\wto$ & & weak convergence to \\
        |\mply| & $\mply$ & |\cdot| & multiplication \\
        \bottomrule
    \end{tabular}
\end{center}

\begin{thebibliography}{1}

\bibitem[Abadir and Magnus, 2002]{Abadir02}
Abadir, K.M., and Magnus, J.R. (2002).
\newblock Notation in Econometrics: A Proposal for a Standard.
\newblock {\em The Econometrics Journal}, 5:76--90  .
\end{thebibliography}

\end{document}
