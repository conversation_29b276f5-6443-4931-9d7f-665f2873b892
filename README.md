# CFA Quant Awards Submission Template

This template is designed for submissions to the CFA Quant Awards, following the official guidelines and based on academic thesis formatting standards.

## Quick Start

1. **Edit the main.tex file**:
   - Replace `\papertitle{Your Paper Title Here}` with your actual title
   - Replace `\candidatename{Your Name}` with your name
   - Replace `\universityname{Your University}` with your institution

2. **Write your content**:
   - Follow the structure provided in the template
   - Focus on practical applications (30% of judging criteria)
   - Keep to 5-7 pages excluding appendices

3. **Add references**:
   - Add your bibliography entries to `references.bib`
   - Use proper citation format throughout

4. **Compile**:
   ```bash
   pdflatex main.tex
   biber main
   pdflatex main.tex
   pdflatex main.tex
   ```

## Key Requirements

### Format
- **Length**: 5-7 pages (excluding appendices)
- **Language**: English
- **Format**: PDF
- **Cover page**: Title ONLY (no author/university name)
- **File naming**: "Quant Awards - [Your Name] - [University Name].pdf"

### Content Focus
The guidelines emphasize **practical applications** over pure technique. Your paper should focus on:
- Real-world relevance and applicability
- Implementation feasibility
- Benefits for practitioners
- Business value and impact

### Judging Criteria
1. **Applicability and relevance (30%)**
2. **Innovation (30%)**
3. **Accuracy and completeness (30%)**
4. **Presentation (10%)**

## Files Included

- `main.tex` - Main document template
- `references.bib` - Bibliography file (add your references here)
- `submission_checklist.md` - Comprehensive submission checklist
- `outline.md` - Detailed paper structure guide
- `README.md` - This file

## Writing Strategy

### Lead with Practical Value
Every section should connect back to practical applications:
- Start with business problems, not technical details
- Explain "why" before "how"
- Include specific use cases and implementation scenarios
- Quantify benefits where possible

### Structure for Success
1. **Hook readers** with a clear problem statement
2. **Establish credibility** through literature review
3. **Demonstrate innovation** in methodology
4. **Prove value** through results
5. **Enable action** through applications discussion

### Common Pitfalls to Avoid
- Overly technical focus without practical context
- Insufficient emphasis on real-world applications
- Poor presentation and unclear writing
- Missing discussion of limitations
- Inadequate validation or robustness checks

## Compilation Requirements

### Required Packages
- `amsmath`, `amsfonts`, `amssymb` - Mathematics
- `biblatex` with APA style - Bibliography
- `graphicx`, `booktabs` - Figures and tables
- `hyperref` - PDF links
- `cleveref` - Smart references
- `geometry` - Page layout

### Build Process
The template uses `biblatex` with `biber` backend:

1. Run `pdflatex main.tex`
2. Run `biber main` (not bibtex)
3. Run `pdflatex main.tex` (twice for cross-references)

## Submission Process

### Registration
- Register before May 31st
- Late registration possible case-by-case

### Submission
- Submit before August 31<NAME_EMAIL>
- File must be named correctly
- Anonymous judging (names replaced with codes)

### Optional Mentoring
- 1-hour coaching session available
- Contact: <EMAIL>
- Topics: topic selection, structure, technical questions

## Tips for Success

### Content Strategy
- Lead with market problems and practical solutions
- Use real market data where possible
- Include implementation costs and benefits
- Address computational feasibility
- Discuss operational considerations

### Presentation
- Use clear, professional language
- Include effective tables and figures
- Maintain logical flow throughout
- Proper citation and formatting
- Focus on clarity over complexity

### Validation
- Include out-of-sample testing
- Perform sensitivity analysis
- Compare with existing methods
- Discuss limitations honestly
- Provide robustness checks

## Support and Resources

- **CFA Guidelines**: See attached PDF
- **Submission Checklist**: See `submission_checklist.md`
- **Paper Outline**: See `outline.md`
- **Mentoring**: <EMAIL>

Good luck with your submission!
