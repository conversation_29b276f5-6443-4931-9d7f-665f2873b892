\chapter{Introduction}
\label{sec:introduction}

This thesis addresses the challenge of modeling and estimating dynamic risk in high-dimensional financial systems. Specifically, I investigate the application of a Bellman Information Filter (BIF) to efficiently estimate Dynamic Factor Stochastic Volatility (DFSV) models. My primary contribution is developing and rigorously evaluating a practical BIF framework tailored for DFSV models, integrating Vector Autoregression (VAR) dynamics and proposing a Block Coordinate Descent (BCD) optimization strategy within the filter's update step. My aim is to make high-dimensional DFSV estimation feasible in practice.

\section{Motivation: The Challenge of Modeling Dynamic Financial Risk}
\label{sec:intro_motivation}

Modeling dynamic financial risk, particularly the co-movement and time-varying dependencies among assets, presents a significant challenge. Static factor models often fail to capture crucial empirical features. The 1998 collapse of Long-Term Capital Management, where inadequate modeling of risk dynamics during the Russian financial crisis led to substantial losses, underscores the importance of accurate dynamic modeling. Asset returns exhibit well-documented stylized facts pertinent to co-movement, including volatility clustering (where periods of high/low volatility persist across assets) and time-varying correlations \parencite{cont_empirical_2001}, alongside heavy tails and leverage effects. While traditional factor models like the Arbitrage Pricing Theory or Fama-French models explain average return structures, their assumption of constant correlations conflicts with empirical observations \parencite{han_asset_2006}. Effectively managing portfolios, assessing risk, and pricing derivatives necessitates models that capture the dynamic nature of risk, specifically the evolving co-movement and shared volatility persistence across assets \parencite{borghi_dynamics_2018}. This requires frameworks that explicitly account for time-varying factor exposures and stochastic volatility driving these dependencies.

\section{DFSV Models: A Framework for Dynamic Co-movement}
\label{sec:intro_dfsv}

Dynamic Factor Stochastic Volatility (DFSV) models offer a powerful framework to model joint, time-varying risk and asset co-movement. They achieve this by introducing latent factors that drive asset returns, where the volatility of these factors evolves stochastically over time. This mechanism generates a time-varying covariance structure that primarily reflects the dynamics of systematic volatility and factor co-movement, aligning the model with observed market phenomena such as volatility clustering and dynamic correlations. Building upon early efforts to integrate stochastic volatility within factor structures \parencite{aguilar_bayesian_2000}, models like the Dynamic Factor Multivariate Stochastic Volatility (DFMSV) framework by \textcite{han_asset_2006} allow both expected returns and volatilities to vary, driven by persistent latent factors with stochastic volatilities. Factor-based Stochastic Volatility (SV) models, like those studied by \textcite{philipov_factor_2006}, provide a structured approach to covariance evolution in high dimensions. DFSV models thus offer a parsimonious yet interpretable representation for complex co-movement patterns, although their estimation remains challenging.

\section{The Estimation Bottleneck: Scaling DFSV Models}
\label{sec:intro_bottleneck}

Despite their theoretical appeal, estimating DFSV models, which are designed to capture dynamic financial co-movement, poses serious computational challenges, particularly in the high-dimensional settings typical of financial applications (i.e., many assets $N$ and factors $K$). The core difficulties stem from the model's characteristics: a large latent state vector (including factors and their log-volatilities) and non-linear and non-Gaussian dynamics due to the stochastic evolution of volatility. Traditional multivariate GARCH models, while simpler, face parameter proliferation issues \parencite{BOLLERSLEV19942959} or impose restrictive correlation structures like in the Dynamic Conditional Correlation (DCC) model \parencite{engle_dynamic_2002}.

Classical state-space estimation methods falter when applied to DFSV models. The standard Kalman Filter (KF) is optimal only for linear and Gaussian systems, assumptions clearly violated by the SV components \parencite{shumway_approach_1982}. Approximate methods like the Extended Kalman Filter (EKF) and Unscented Kalman Filter (UKF) rely on local linearizations or specific distributional assumptions (e.g., Gaussian approximations via moment matching). These approximations can introduce significant bias and may struggle with the strong non-linearities and potentially multi-modal distributions induced by SV, leading to inaccurate state estimates or filter divergence, especially over long time series \parencite{lange_bellman_2024}.

Simulation-based approaches offer more flexibility but face their own scalability issues. Bayesian Markov Chain Monte Carlo (MCMC) methods provide a gold standard for inference in complex latent variable models \parencite{kim_stochastic_1998}. However, for high-dimensional state spaces like those in DFSV models, MCMC algorithms often mix slowly and converge slowly, requiring extremely long simulation runs to explore the posterior distribution adequately, making them computationally intensive \parencite{kastner_efficient_2017}. Particle Filters (PF), also known as Sequential Monte Carlo (SMC) methods, directly approximate the filtering distribution using a set of weighted samples (particles). While theoretically capable of handling arbitrary non-linearities and non-Gaussianities, PFs face an exponential particle-count growth in state dimension: the number of particles required to maintain a given level of accuracy grows exponentially with the dimension of the state vector \parencite{malik_modelling_2011, lange_bellman_2024}. In practice, for DFSV models with even moderate $N$ and $K$, the required particle count becomes computationally prohibitive, and the filter often succumbs to weight degeneracy (where only a few particles have non-negligible weights), leading to unstable and unreliable estimates \parencite{rebeschini_can_2015}. This computational bottleneck severely limits the practical application of sophisticated DFSV models.

\section{The Bellman Filter: A Novel Estimation Approach}
\label{sec:intro_bif}

The Bellman filter, a recently developed method inspired by Bellman's dynamic programming principle, offers an alternative estimator \parencite{lange_bellman_2024}. Instead of attempting to approximate the full filtering distribution via integration (like KF) or simulation (like PF), it recasts the filtering update as a sequential optimization problem, seeking the mode of the posterior state distribution. This mode-based approach, typically implemented using an iterative quadratic approximation to the log-posterior density (akin to a Laplace approximation), efficiently handles the non-linear and non-Gaussian dynamics inherent in DFSV models. \textcite{lange_bellman_2024} demonstrates that the Bellman filter generalizes Kalman-based filters while maintaining polynomial computational complexity, scaling cubically with the state dimension ($O(m^3)$), making it feasible for the large state vectors encountered in DFSV applications.

Because it is deterministic, BIF side-steps particle degeneracy. It provides a deterministic approximation to the posterior mode and covariance, offering a compelling speed-accuracy trade-off \parencite{lange_bellman_2024}. Furthermore, the filter has desirable stability properties, preventing error accumulation over long time series. A key practical advantage is that the Bellman filter naturally produces an approximate pseudo-likelihood function as a by-product of its recursive updates. This function can be maximized using standard numerical optimization techniques to estimate the model's static parameters (hyperparameters). This provides a computationally feasible route to parameter estimation within a quasi-maximum likelihood framework, avoiding the complexities of likelihood evaluation via particle smoothing or the computational burden of full MCMC procedures.

This potential makes the Bellman filter a promising candidate for addressing the estimation challenges in high-dimensional DFSV models, which is the focus of the research questions presented in the following section.
\section{Research Questions and Contributions}
\label{sec:intro_rq_contrib}

I adapt the Information-form Bellman filter (BIF) to high-dimensional DFSV models. My aim is to connect the expressive power of DFSV models with practical estimation methods, addressing the limitations of existing techniques highlighted in Section~\ref{sec:intro_bottleneck}. Specifically, I address four inter-related research questions and the corresponding contributions:

\begin{table}[htb]
\small
\begin{tabularx}{\textwidth}{@{}lX@{}}
\toprule
\textbf{RQ 1} & \textbf{Method engineering:} How can the Bellman Information Filter be customized—through block-coordinate descent, VAR dynamics and Fisher-information updates—to deliver \textit{deterministic} state \textit{and} hyperparameter estimation for high-dimensional DFSV models, and what is the resulting computational complexity? \textit{(Answered in Sections~\ref{sec:bif_update}--\ref{sec:methodology:model_summary} and Appendix~\ref{app:mathematical_derivations})} \\
\midrule
\textbf{RQ 2} & \textbf{Simulation benchmarking:} Across increasing asset (N) and factor (K) dimensions, how does DFSV-BIF compare with particle filtering in runtime scaling, numerical stability, factor-state accuracy and volatility-state accuracy? \textit{(Answered in Chapter~\ref{ch:simulation})} \\
\midrule
\textbf{RQ 3} & \textbf{Empirical performance versus standard industry models:} On a panel of 95 Fama–French size/BM portfolios (1963-2023), does DFSV-BIF improve the modelling and forecasting of time-varying covariances and market co-movement relative to (i) DFSV-PF, (ii) DCC-GARCH(1,1) and (iii) a constant-volatility DFM, when judged under a six-pillar evaluation framework (statistical fit, covariance dynamics, factor loadings, residual diagnostics, parameter plausibility, computational cost)? \textit{(Answered in Chapters~\ref{ch:data_evaluation_simulation} and \ref{ch:empirical_application})} \\
\midrule
\textbf{RQ 4} & \textbf{Diagnostic insight and model limitations:} To what extent does the assumption of constant diagonal idiosyncratic variance in DFSV models hinder residual normality and ARCH behaviour, and what extensions (e.g., grouped or asset-level idiosyncratic SV) are indicated by the diagnostic evidence? \textit{(Answered in Section~\ref{sec:empirical:residual_puzzle} and Chapter~\ref{ch:conclusion})} \\
\bottomrule
\end{tabularx}
\caption{Research Questions and Corresponding Contributions.}
\label{tab:research_questions}
\end{table}
My primary contribution is developing and rigorously evaluating a practical BIF framework tailored for DFSV models, which are used to model dynamic financial co-movement. This involves integrating the BIF with VAR dynamics and proposing a specialized BCD optimization strategy within the filter's update step to handle the specific structure of the DFSV estimation problem efficiently (addressing RQ 1). I contrast this approach with the literature, particularly the challenges identified for KF, PF, and MCMC methods. While \textcite{lange_bellman_2024} introduced the general Bellman filter, its application and adaptation to the specific structure and scale of financial DFSV models, including the BCD enhancement, is the core advancement here. I demonstrate through simulation studies that the BIF delivers substantial computational speedups and comparable or superior state estimation accuracy, particularly for latent log-volatilities, compared to benchmark particle filters, especially in high-dimensional scenarios. An empirical application will further demonstrate the framework's practical utility, thereby addressing the simulation benchmarking (RQ 2), empirical comparison (RQ 3), and diagnostic analysis (RQ 4) outlined above.

% Optional: Thesis Outline section could be added here if desired.
