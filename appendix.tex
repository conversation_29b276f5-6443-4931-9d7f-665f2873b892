\chapter{Mathematical Derivations}\label{app:mathematical_derivations}

This appendix provides detailed mathematical derivations that support the methodology presented in Chapter~\ref{ch:methodology}. It includes matrix identities, log-likelihood derivations, optimization algorithms, and Fisher Information Matrix calculations that are essential to the implementation of the Bellman Information Filter.

\section{Matrix Identities for Efficient Computation}\label{app:matrix_identities}

This section provides a comprehensive overview of the matrix identities used throughout the thesis for efficient computation in high-dimensional settings. These identities are fundamental to the implementation of the Bellman Information Filter and are referenced in various parts of the methodology.

\subsection{Key Matrix Identities}

Two key matrix identities are used throughout the implementation of the Bellman Information Filter to enable efficient computation in high-dimensional settings.

\paragraph{The Woodbury Matrix Identity:}
For matrices $\boldsymbol{A} \in \mathbb{R}^{n \times n}$, $\boldsymbol{U} \in \mathbb{R}^{n \times k}$, $\boldsymbol{C} \in \mathbb{R}^{k \times k}$, and $\boldsymbol{V} \in \mathbb{R}^{k \times n}$, the Woodbury identity states:

\begin{equation}\label{eq:<PERSON><PERSON>}
(\boldsymbol{A} + \boldsymbol{U}\boldsymbol{C}\boldsymbol{V})^{-1} = \boldsymbol{A}^{-1} - \boldsymbol{A}^{-1}\boldsymbol{U}(\boldsymbol{C}^{-1} + \boldsymbol{V}\boldsymbol{A}^{-1}\boldsymbol{U})^{-1}\boldsymbol{V}\boldsymbol{A}^{-1}.
\end{equation}

\paragraph{The Matrix Determinant Lemma:}
For matrices $\boldsymbol{A} \in \mathbb{R}^{n \times n}$ and $\boldsymbol{U}, \boldsymbol{V} \in \mathbb{R}^{n \times k}$, the determinant can be computed as:

\begin{equation}\label{eq:det_lemma}
\det(\boldsymbol{A} + \boldsymbol{U}\boldsymbol{V}^\top) = \det(\boldsymbol{A}) \cdot \det(\boldsymbol{I}_k + \boldsymbol{V}^\top\boldsymbol{A}^{-1}\boldsymbol{U}).
\end{equation}

Both identities reduce computational complexity from $O(n^3)$ to $O(nk^2 + k^3)$ when $k \ll n$, which is a significant improvement for our DFSV models where $n$ represents the number of assets $N$ and $k$ represents the number of factors $K$, with $K \ll N$.

\subsection{Application in DFSV Models}

In the context of DFSV models, these identities are applied to:

\paragraph{1. Observation Covariance Matrix Inverse:}
The observation covariance matrix in the DFSV model has the form:
\begin{equation}
\boldsymbol{\Sigma}_t = \mLambda\Diag(e^{\vh_t})\mLambda^\top + \mSigma_{\epsilon}.
\end{equation}

This can be written in the form $\boldsymbol{A} + \boldsymbol{U}\boldsymbol{C}\boldsymbol{V}$ where:
\begin{align}
\boldsymbol{A} &= \mSigma_{\epsilon} \quad \text{(diagonal matrix of idiosyncratic variances)}\\
\boldsymbol{U} &= \mLambda \quad \text{(factor loading matrix)}\\
\boldsymbol{C} &= \Diag(e^{\vh_t}) \quad \text{(diagonal matrix of factor variances)}\\
\boldsymbol{V} &= \mLambda^\top
\end{align}

Applying the Woodbury identity, we get:
\begin{align}
\boldsymbol{\Sigma}_t^{-1} &= (\mSigma_{\epsilon} + \mLambda\Diag(e^{\vh_t})\mLambda^\top)^{-1} \\
&= \mSigma_{\epsilon}^{-1} - \mSigma_{\epsilon}^{-1}\mLambda(\Diag(e^{-\vh_t}) + \mLambda^\top\mSigma_{\epsilon}^{-1}\mLambda)^{-1}\mLambda^\top\mSigma_{\epsilon}^{-1}.
\end{align}

\paragraph{2. Log-Determinant Calculation:}
Similarly, for the log-determinant term in the likelihood calculation:
\begin{align}
\log|\boldsymbol{\Sigma}_t| &= \log|\mSigma_{\epsilon} + \mLambda\Diag(e^{\vh_t})\mLambda^\top| \\
&= \log|\mSigma_{\epsilon}| + \log|\boldsymbol{I}_K + \Diag(e^{\vh_t})\mLambda^\top\mSigma_{\epsilon}^{-1}\mLambda| \\
&= \sum_{i=1}^N \log(\sigma_i^2) + \log|\Diag(e^{\vh_t})| + \log|\Diag(e^{-\vh_t}) + \mLambda^\top\mSigma_{\epsilon}^{-1}\mLambda|.
\end{align}

\paragraph{3. Information Matrix Prediction:}
In the BIF prediction step, the Woodbury identity is used to efficiently compute the predicted information matrix:
\begin{equation}
\boldsymbol{\Omega}_{t|t-1} = \boldsymbol{Q}_t^{-1} - \boldsymbol{Q}_t^{-1}\mT\boldsymbol{M}^{-1}\mT^\top\boldsymbol{Q}_t^{-1}.
\end{equation}
Where $\boldsymbol{M} = \boldsymbol{\Omega}_{t-1|t-1} + \mT^\top\boldsymbol{Q}_t^{-1}\mT$.

\paragraph{4. Fisher Information Matrix Calculation:}
The Woodbury identity is also used in computing the Fisher Information Matrix for the update step, particularly in the calculation of the blocks $\boldsymbol{J}_{ff}$, $\boldsymbol{J}_{fh}$, and $\boldsymbol{J}_{hh}$. See Section~\ref{app:fim} for a detailed derivation of the Fisher Information Matrix.

\section{Log-Likelihood Derivation and Efficient Computation}\label{app:bf_loglik}

This appendix provides a detailed derivation of the log-likelihood for the DFSV model and the efficient computation methods used to evaluate it. The log-likelihood is a key component in both the Bellman Information Filter and Particle Filter implementations, and its efficient computation is crucial for handling high-dimensional data.

\subsection{Derivation of the DFSV Model Log-Likelihood Formulations}

The log-likelihood of the DFSV model consists of several components, with the observation log-likelihood being particularly important for filtering. In this section, I derive two key formulations of the observation log-likelihood that are used in different filtering approaches: the marginal log-likelihood used in the Bellman Information Filter and the conditional log-likelihood used in the Particle Filter.

\subsubsection{Observation Equation and Probability Model}

The observation equation of the DFSV model is given by:
\begin{equation}
\vr_t = \mLambda \vf_t + \vepsilon_t, \quad \vepsilon_t \sim N\bigl(\mathbf{0},\ \mSigma_{\epsilon}\bigr).
\end{equation}

This equation defines a hierarchical probability model where the distribution of returns depends on both the factors and log-volatilities. We can express this in two different ways, leading to two different log-likelihood formulations.

\subsubsection{Conditional Log-Likelihood (Used in Particle Filter)}

The first formulation is the conditional log-likelihood of observations given only the factors. This is derived directly from the observation equation by considering the distribution of the idiosyncratic error term $\bm{\epsilon}_t$:

\begin{align}
\vr_t | \vf_t &\sim N(\mLambda\vf_t, \mSigma_{\epsilon}) \\
\log p(\vr_t | \vf_t) &= \log \left[ \frac{1}{(2\pi)^{N/2}|\mSigma_{\epsilon}|^{1/2}} \exp\left(-\frac{1}{2}(\vr_t - \mLambda\vf_t)'\mSigma_{\epsilon}^{-1}(\vr_t - \mLambda\vf_t)\right) \right] \\
&= -\frac{N}{2}\log(2\pi) - \frac{1}{2}\log|\mSigma_{\epsilon}| - \frac{1}{2}(\vr_t - \mLambda\vf_t)'\mSigma_{\epsilon}^{-1}(\vr_t - \mLambda\vf_t).
\end{align}

This can be written more compactly as:
\begin{equation}\label{eq:cond_loglik_expanded}
\log p(\vr_t | \vf_t) = -\frac{1}{2}\left[N\log(2\pi) + \log|\mSigma_{\epsilon}| + (\vr_t - \mLambda\vf_t)'\mSigma_{\epsilon}^{-1}(\vr_t - \mLambda\vf_t)\right].
\end{equation}

This is the conditional log-likelihood defined in Equation~\ref{eq:cond_log_likelihood} in the main text. It is used in the Particle Filter to compute importance weights for each particle, as it directly evaluates how well the observed returns match the predicted returns given a specific realization of the factors.

The key characteristic of this formulation is that it uses only the idiosyncratic covariance matrix $\mSigma_{\epsilon}$, which is diagonal and constant over time. This makes it computationally efficient to evaluate for each particle in the filter.

\subsubsection{Marginal Log-Likelihood (Used in Bellman Information Filter)}

The second formulation is the marginal log-likelihood of observations given the complete state vector $\valpha_t = [\vf_t', \vh_t']'$. This considers the joint distribution of returns and factors, marginalizing over the uncertainty in the factors (conditional on the log-volatilities).

To derive this, we first note that the factors $\vf_t$ have a distribution conditional on the log-volatilities $\vh_t$:
\begin{equation}
\vf_t | \vh_t \sim N(\hat{\vf}_{t|t-1}, \Diag(e^{\vh_t})).
\end{equation}

Where $\hat{\vf}_{t|t-1}$ is the predicted factor mean from the previous time step. Combining this with the observation equation, we get the marginal distribution of returns conditional on log-volatilities:

\begin{align}
\vr_t | \vh_t &\sim N(\mLambda\hat{\vf}_{t|t-1}, \mLambda\Diag(e^{\vh_t})\mLambda' + \mSigma_{\epsilon}) \\
&\sim N(\mLambda\hat{\vf}_{t|t-1}, \boldsymbol{\Sigma}_t).
\end{align}

Where $\boldsymbol{\Sigma}_t = \mLambda\Diag(e^{\vh_t})\mLambda' + \mSigma_{\epsilon}$ is the marginal observation covariance matrix that accounts for both factor uncertainty and idiosyncratic noise.

The log-likelihood of this multivariate normal distribution is:
\begin{align}
\log p(\vr_t | \valpha_t) &= \log \left[ \frac{1}{(2\pi)^{N/2}|\boldsymbol{\Sigma}_t|^{1/2}} \exp\left(-\frac{1}{2}(\vr_t - \mLambda\bm{f}_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\bm{f}_t)\right) \right] \\
&= -\frac{N}{2}\log(2\pi) - \frac{1}{2}\log|\boldsymbol{\Sigma}_t| - \frac{1}{2}(\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t).
\end{align}

This can be written more compactly as:
\begin{equation}\label{eq:marg_loglik_expanded}
\ell(\vr_t | \valpha_t) = -\frac{1}{2}\left[N\log(2\pi) + \log|\boldsymbol{\Sigma}_t| + (\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t)\right].
\end{equation}

This is the marginal log-likelihood defined in Equation~\ref{eq:log_likelihood} in the main text. It is used in the Bellman Information Filter's objective function and its pseudo-likelihood calculation.

\subsubsection{Comparison of the Two Formulations}

The key difference between these two formulations lies in the covariance matrix used:

\begin{itemize}
    \item \textbf{Conditional Log-Likelihood:} Uses only $\mSigma_{\epsilon}$ (idiosyncratic covariance)
    \item \textbf{Marginal Log-Likelihood:} Uses $\boldsymbol{\Sigma}_t = \mLambda\Diag(e^{\vh_t})\mLambda' + \mSigma_{\epsilon}$ (marginal covariance)
\end{itemize}

The marginal formulation accounts for both the uncertainty in the factors (through the term $\mLambda\Diag(e^{\vh_t})\mLambda'$) and the idiosyncratic noise (through $\mSigma_{\epsilon}$). This makes it more appropriate for the Bellman filter, which aims to find the posterior mode of the complete state vector $\valpha_t$ by maximizing the log-posterior density.

The conditional formulation, on the other hand, is more appropriate for the Particle Filter, which represents the full posterior distribution through a set of weighted particles. Each particle already represents a specific realization of the state, so the conditional likelihood is used to evaluate how well each particle explains the observed returns.

Both formulations play crucial roles in their respective filtering approaches and contribute to the overall parameter estimation process.

\subsubsection{Factor Transition Log-Likelihood}

The factor transition equation is:
\begin{equation}
\vf_{t+1} = \mPhi_f \,\vf_t + \vnu_{t+1}, \quad \vnu_{t+1} \sim N\!\left(\mathbf{0},\ \Diag\Bigl(e^{h_{1,t+1}},\dots,e^{h_{K,t+1}}\Bigr)\right).
\end{equation}

The conditional distribution of $\vf_{t+1}$ given $\vf_t$ and $\vh_{t+1}$ is:
\begin{equation}
\vf_{t+1} | \vf_t, \vh_{t+1} \sim N(\mPhi_f\vf_t, \Diag(e^{\vh_{t+1}})).
\end{equation}

The log-likelihood of this distribution is:
\begin{align}
\log p(\vf_{t+1} | \vf_t, \vh_{t+1}) &= -\frac{K}{2}\log(2\pi) - \frac{1}{2}\log|\Diag(e^{\vh_{t+1}})| \\
&\quad - \frac{1}{2}(\vf_{t+1} - \mPhi_f\vf_t)'\Diag(e^{-\vh_{t+1}})(\vf_{t+1} - \mPhi_f\vf_t).
\end{align}

Since $\log|\Diag(e^{\vh_{t+1}})| = \sum_{k=1}^K h_{k,t+1}$ and the quadratic form with a diagonal matrix can be simplified, we get:
\begin{align}
\log p(\vf_{t+1} | \vf_t, \vh_{t+1}) &= -\frac{K}{2}\log(2\pi) - \frac{1}{2}\sum_{k=1}^K h_{k,t+1} \\
&\quad - \frac{1}{2}\sum_{k=1}^K e^{-h_{k,t+1}}(f_{k,t+1} - [\mPhi_f\vf_t]_k)^2.
\end{align}

This formulation avoids the numerical issues that can arise from computing $\log(\exp(\cdot))$ directly.

\subsubsection{Log-Volatility Transition Log-Likelihood}

The log-volatility transition equation is:
\begin{equation}
\vh_{t+1} = \vmu + \mPhi_h\,(\vh_t - \vmu) + \veta_{t+1}, \quad \veta_{t+1}\sim N\!\left(\mathbf{0},\, \mQ_h\right).
\end{equation}

The conditional distribution of $\vh_{t+1}$ given $\vh_t$ is:
\begin{equation}
\vh_{t+1} | \vh_t \sim N(\vmu + \mPhi_h(\vh_t - \vmu), \mQ_h).
\end{equation}

The log-likelihood of this distribution is:
\begin{align}
\log p(\vh_{t+1} | \vh_t) &= -\frac{K}{2}\log(2\pi) - \frac{1}{2}\log|\mQ_h| \\
&\quad - \frac{1}{2}(\vh_{t+1} - \vmu - \mPhi_h(\vh_t - \vmu))'\mQ_h^{-1}(\vh_{t+1} - \vmu - \mPhi_h(\vh_t - \vmu)).
\end{align}

\subsubsection{Joint Log-Likelihood}

The joint log-likelihood of the entire observation sequence $\{\vr_t\}_{t=1}^T$ and state sequence $\{\valpha_t\}_{t=1}^T$ is the sum of the individual components:
\begin{align}
\log p(\{\vr_t\}_{t=1}^T, \{\valpha_t\}_{t=1}^T) &= \log p(\valpha_1) + \sum_{t=1}^T \log p(\vr_t | \valpha_t) \\
&\quad + \sum_{t=1}^{T-1} \log p(\vf_{t+1} | \vf_t, \vh_{t+1}) + \sum_{t=1}^{T-1} \log p(\vh_{t+1} | \vh_t).
\end{align}

Where $\log p(\valpha_1)$ is the log-likelihood of the initial state, typically assumed to be a diffuse prior or specified based on stationarity properties of the model.

In the context of filtering, we are often interested in the marginal log-likelihood of the observations, which requires integrating out the states:
\begin{equation}
\log p(\{\vr_t\}_{t=1}^T) = \log \int p(\{\vr_t\}_{t=1}^T, \{\valpha_t\}_{t=1}^T) d\{\valpha_t\}_{t=1}^T.
\end{equation}

This integration is analytically intractable for the DFSV model due to the non-linear relationship between the log-volatilities and the observation covariance matrix. Both the Bellman Information Filter and Particle Filter provide approximations to this marginal likelihood.

\subsection{Efficient Computation via Matrix Identities}

Direct computation of the observation log-likelihood (Equation~\ref{eq:marg_loglik_expanded}) is challenging due to the high-dimensional matrix operations involved, particularly when $N \gg K$. The computational complexity would be $O(N^3)$ for the determinant and matrix inversion operations.

To address this, we use the matrix identities described in Section~\ref{app:matrix_identities}. Specifically, we apply the Matrix Determinant Lemma (Equation~\ref{eq:det_lemma}) and the Woodbury Matrix Identity (Equation~\ref{eq:woodbury}) to efficiently compute the log-likelihood.

\subsubsection{Efficient Computation of $\log|\boldsymbol{\Sigma}_t|$}

Using the Matrix Determinant Lemma, we can compute the log-determinant term as:
\begin{align}
\log|\boldsymbol{\Sigma}_t| &= \log|\mSigma_{\epsilon} + \mLambda\Diag(e^{\vh_t})\mLambda'| \\
&= \log|\mSigma_{\epsilon}| + \log|\Diag(e^{\vh_t})| + \log|\Diag(e^{-\vh_t}) + \mLambda'\mSigma_{\epsilon}^{-1}\mLambda| \\
&= \sum_{i=1}^N \log(\sigma_i^2) + \sum_{k=1}^K h_{k,t} + \log|\Diag(e^{-\vh_t}) + \mLambda'\mSigma_{\epsilon}^{-1}\mLambda|.
\end{align}

This reduces the computational complexity from $O(N^3)$ to $O(NK^2 + K^3)$ since we only need to compute the determinant of a $K \times K$ matrix instead of an $N \times N$ matrix.

\subsubsection{Efficient Computation of the Quadratic Form}

Using the Woodbury Matrix Identity, we can compute the inverse of the observation covariance matrix as:
\begin{align}
\boldsymbol{\Sigma}_t^{-1} &= (\mSigma_{\epsilon} + \mLambda\Diag(e^{\vh_t})\mLambda')^{-1} \\
&= \mSigma_{\epsilon}^{-1} - \mSigma_{\epsilon}^{-1}\mLambda(\Diag(e^{-\vh_t}) + \mLambda'\mSigma_{\epsilon}^{-1}\mLambda)^{-1}\mLambda'\mSigma_{\epsilon}^{-1}.
\end{align}

This allows us to compute the quadratic form in the log-likelihood without explicitly forming the $N \times N$ inverse matrix:
\begin{align}
(\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t) &= (\vr_t - \mLambda\vf_t)'\mSigma_{\epsilon}^{-1}(\vr_t - \mLambda\vf_t) \\
&\quad - (\vr_t - \mLambda\vf_t)'\mSigma_{\epsilon}^{-1}\mLambda(\Diag(e^{-\vh_t}) + \mLambda'\mSigma_{\epsilon}^{-1}\mLambda)^{-1}\mLambda'\mSigma_{\epsilon}^{-1}(\vr_t - \mLambda\vf_t).
\end{align}

Again, this reduces the computational complexity from $O(N^3)$ to $O(NK^2 + K^3)$.

\subsection{Efficient Implementation}

By applying these matrix identities, the computational complexity of key operations in the BIF is reduced from $O(N^3)$ to $O(NK^2 + K^3)$, which is a significant improvement when $N \gg K$. The implementation includes numerical stability enhancements such as Cholesky decomposition for matrix inversions and small jitter terms to prevent numerical issues.

The JAX implementation of these computations leverages automatic differentiation and just-in-time compilation for further efficiency gains. The log-likelihood calculation is a core component of both the filtering algorithms and the parameter estimation procedures, making these optimizations crucial for the practical application of DFSV models to high-dimensional financial data.

\section{Block Coordinate Descent for Bellman Filter State Update}\label{app:bcd_update}

This section provides a detailed mathematical derivation of the Block Coordinate Descent (BCD) algorithm used in the Bellman Information Filter to find the posterior mode. While the main text in Section~\ref{sec:bif_update} outlines the general approach and algorithm, here we focus on the mathematical details and computational considerations that make BCD particularly efficient for the DFSV model.

The Bellman filter update step aims to find the posterior mode $\hat{\valpha}_{t|t}$ by maximizing the log posterior density. This is equivalent to maximizing the following objective function (as defined in Equation~\ref{eq:objective_function} in the main text):

\begin{equation}
J(\valpha_t) = \ell(\vr_t \mid \valpha_{t}) - \frac{1}{2}\left(\valpha_t - \hat{\valpha}_{t|t-1}\right)' \boldsymbol{\Omega}_{t|t-1}\left(\valpha_t - \hat{\valpha}_{t|t-1}\right).
\end{equation}

Here, $\valpha_t = [\vf_t', \vh_t']'$ is the state vector containing both factors $\vf_t$ and log-volatilities $\vh_t$, $\boldsymbol{\Omega}_{t|t-1}$ is the predicted information matrix, and $\ell(\vr_t \mid \valpha_{t})$ is the observation log-likelihood. Due to the non-linear relationship between $\vh_t$ and the observation covariance matrix $\boldsymbol{\Sigma}_t$, direct optimization of this objective function with respect to the full state vector $\valpha_t$ is challenging. The BCD approach addresses this by alternating between optimizing the factors $\vf_t$ and log-volatilities $\vh_t$, which leads to more tractable subproblems.


\subsection{Factor Update Step: Closed-Form Solution}\label{app:factor_update}

The key advantage of the BCD approach for DFSV models is that the factor update step has a closed-form solution. When maximizing the Bellman objective function $J(\valpha_t)$ from Equation~\ref{eq:objective_function} with respect to $\vf_t$ while keeping $\vh_t = \vh^{(k)}$ fixed, we can derive a linear system by setting the gradient to zero:

\begin{equation}
\frac{\partial J(\valpha_t)}{\partial \vf_t} = \frac{\partial \ell(\vr_t \mid \vf_t, \vh^{(k)})}{\partial \vf_t} - \boldsymbol{\Omega}_{ff}(\vf_t - \vf_{t|t-1}) - \boldsymbol{\Omega}_{fh}(\vh^{(k)} - \vh_{t|t-1}) = \mathbf{0}.
\end{equation}

With fixed $\vh^{(k)}$, the observation covariance matrix $\boldsymbol{\Sigma}_t = \mLambda\,\Diag(e^{\vh^{(k)}})\,\mLambda' + \mSigma_{\epsilon}$ is constant with respect to $\vf_t$. The log-likelihood gradient simplifies to $\frac{\partial \ell(\vr_t \mid \vf_t, \vh^{(k)})}{\partial \vf_t} = \mLambda'\boldsymbol{\Sigma}_t^{-1}\vr_t - \mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda\vf_t$.

Substituting and rearranging terms leads to the linear system:

\begin{equation}
(\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda + \boldsymbol{\Omega}_{ff})\vf_t = \mLambda'\boldsymbol{\Sigma}_t^{-1}\vr_t + \boldsymbol{\Omega}_{ff}\vf_{t|t-1} - \boldsymbol{\Omega}_{fh}(\vh^{(k)} - \vh_{t|t-1}).
\end{equation}

The solution gives the updated factors:

\begin{equation}
\vf^{(k+1)} = (\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda + \boldsymbol{\Omega}_{ff})^{-1}(\mLambda'\boldsymbol{\Sigma}_t^{-1}\vr_t + \boldsymbol{\Omega}_{ff}\vf_{t|t-1} - \boldsymbol{\Omega}_{fh}(\vh^{(k)} - \vh_{t|t-1})).
\end{equation}

This is the same update equation presented in the main text, but here we've provided the detailed derivation showing how it arises from the gradient of the objective function.


\subsection{Log-Volatility Update Step: Numerical Optimization}\label{app:logvol_update}

Unlike the factor update, the log-volatility update step requires numerical optimization due to the non-linear relationship between $\vh_t$ and the observation covariance matrix. This section details the mathematical challenges and solutions for this step.

\paragraph{Non-linearity Challenge:}
The key challenge in the log-volatility update is that the observation covariance matrix $\boldsymbol{\Sigma}_t = \mLambda\,\Diag(e^{\vh_t})\,\mLambda' + \mSigma_{\epsilon}$ depends exponentially on $\vh_t$. This creates non-linearities in both the log-determinant term and the quadratic form in the log-likelihood (Equation~\ref{eq:log_likelihood}), where the factors are fixed at $\vf^{(k+1)}$.

When optimizing the objective function $J(\valpha_t)$ from Equation~\ref{eq:objective_function} with respect to $\vh_t$ while keeping $\vf_t = \vf^{(k+1)}$ fixed, the optimization problem becomes:

\begin{align}
\vh^{(k+1)} = \arg\max_{\vh_t} \Bigg\{&\ell(\vr_t \mid \vf^{(k+1)}, \vh_t) \\
&- \frac{1}{2}(\vf^{(k+1)} - \hat{\vf}_{t|t-1})'\boldsymbol{\Omega}_{ff}(\vf^{(k+1)} - \hat{\vf}_{t|t-1}) \\
&- (\vf^{(k+1)} - \hat{\vf}_{t|t-1})'\boldsymbol{\Omega}_{fh}(\vh_t - \hat{\vh}_{t|t-1}) \\
&- \frac{1}{2}(\vh_t - \hat{\vh}_{t|t-1})'\boldsymbol{\Omega}_{hh}(\vh_t - \hat{\vh}_{t|t-1})\Bigg\}.
\end{align}

Since $\vf^{(k+1)}$ is fixed in this step, the term involving $\boldsymbol{\Omega}_{ff}$ is constant and can be omitted from the optimization. The problem then simplifies to:

\begin{align}
\vh^{(k+1)} = \arg\max_{\vh_t} \Bigg\{&\ell(\vr_t \mid \vf^{(k+1)}, \vh_t) \\
&- (\vf^{(k+1)} - \hat{\vf}_{t|t-1})'\boldsymbol{\Omega}_{fh}(\vh_t - \hat{\vh}_{t|t-1}) \\
&- \frac{1}{2}(\vh_t - \hat{\vh}_{t|t-1})'\boldsymbol{\Omega}_{hh}(\vh_t - \hat{\vh}_{t|t-1})\Bigg\}.
\end{align}

\paragraph{Gradient-Based Optimization and Implementation:}
To solve this non-linear optimization problem, we use a trust-region variant of the BFGS algorithm to maximize the objective function. The implementation leverages JAX's automatic differentiation capabilities rather than manually computing derivatives. This approach defines the objective function for the log-volatility update, and JAX automatically computes the gradients needed for optimization, improving accuracy and maintainability while simplifying the codebase.

\subsection{Computational Efficiency Considerations}\label{app:bcd_efficiency}

The BCD algorithm alternates between the factor update and log-volatility update steps until convergence (typically within 10 iterations). This approach offers significant computational advantages over direct optimization of the full state vector $\valpha_t$ for several reasons:

First, by optimizing over $K$ dimensions at a time rather than $2K$ dimensions simultaneously, each sub-problem becomes more tractable. The factor update step yields a closed-form solution, avoiding expensive iterative optimization for half of the state vector. This reduces computational complexity from $O(N^3)$ to $O(NK^2 + K^3)$ for high-dimensional problems with many assets ($N \gg K$).

Second, several numerical stability techniques are employed, including Cholesky decomposition for solving linear systems, small jitter terms to ensure positive definiteness of matrices, and the Woodbury identity to efficiently compute $\boldsymbol{\Sigma}_t^{-1}$ without directly inverting high-dimensional matrices.

For the log-volatility optimization specifically, a trust-region approach constrains step sizes to maintain validity of the quadratic approximation in the BFGS algorithm, with fallback strategies ensuring robustness if optimization fails to converge.

These efficiency considerations make the BCD approach particularly well-suited for DFSV models in high-dimensional settings, allowing the Bellman Information Filter to efficiently handle problems that would be computationally prohibitive with direct optimization methods.


\section{Fisher Information Matrix in DFSV Models}\label{app:fim}

This section provides a detailed explanation of the Fisher Information Matrix (FIM) as used in the Bellman Information Filter for DFSV models. The FIM plays a crucial role in the BIF update step, where it represents the curvature of the log-likelihood function at the posterior mode.

\subsection{Definition and Role in Bellman Filtering}

In the context of the Bellman Information Filter, the Fisher Information Matrix is used to update the information matrix in the filtering recursion:

\begin{equation}
\boldsymbol{\Omega}_{t|t} = \boldsymbol{\Omega}_{t|t-1} + \Infmat_t.
\end{equation}

Where $\boldsymbol{\Omega}_{t|t}$ is the posterior information matrix, $\boldsymbol{\Omega}_{t|t-1}$ is the predicted information matrix, and $\Infmat_t$ is the Fisher Information Matrix evaluated at the posterior mode $\hat{\valpha}_{t|t}$.

\subsection{Observed vs. Expected Fisher Information}

There are two main formulations of the Fisher Information Matrix:

\paragraph{1. Observed Fisher Information Matrix (O-FIM):}
The Observed Fisher Information Matrix is defined as the negative Hessian of the log-likelihood function evaluated at the posterior mode:

\begin{equation}
\Infmat_t^{\text{obs}} = -\nabla^2 \log p(\vr_t \mid \valpha_{t})|_{\valpha_{t} = \hat{\valpha}_{t|t}}.
\end{equation}

This matrix captures the local curvature of the log-likelihood function at the posterior mode.

\paragraph{2. Expected Fisher Information Matrix (E-FIM):}
The Expected Fisher Information Matrix is defined as the expectation of the negative Hessian of the log-likelihood function:

\begin{equation}
\Infmat_t^{\text{exp}} = \mathbb{E}[-\nabla^2 \log p(\vr_t \mid \valpha_{t})]|_{\valpha_{t} = \hat{\valpha}_{t|t}}.
\end{equation}

The expectation is taken with respect to the distribution of the observations $\vr_t$ given the state $\valpha_{t}$.

\paragraph{Choice in BIF Implementation:}
The BIF implementation uses the E-FIM rather than the O-FIM. This choice is motivated by several considerations:

\begin{itemize}
    \item The E-FIM is guaranteed to be positive semi-definite (PSD), which ensures numerical stability in the information matrix update.
    \item The O-FIM may not be PSD in practice due to numerical issues or model misspecification, requiring eigenvalue clipping or other regularization techniques.
    \item The E-FIM often has a simpler analytical form that can be computed more efficiently.
\end{itemize}

\subsection{Block Structure of the Fisher Information Matrix}

For the DFSV model, the Fisher Information Matrix has a block structure corresponding to the partitioning of the state vector $\valpha_t = [\vf_t', \vh_t']'$:

\begin{equation}
\Infmat_t = \begin{bmatrix} \Infmat_{ff} & \Infmat_{fh} \\ \Infmat_{fh}' & \Infmat_{hh} \end{bmatrix}.
\end{equation}

Where:
\begin{itemize}
    \item $\Infmat_{ff}$ represents the curvature with respect to the factors $\vf_t$
    \item $\Infmat_{hh}$ represents the curvature with respect to the log-volatilities $\vh_t$
    \item $\Infmat_{fh}$ represents the cross-derivatives between factors and log-volatilities
\end{itemize}

\subsection{Derivation of the E-FIM}

For the DFSV model with observation equation $\vr_t = \mLambda \vf_t + \vepsilon_t$, where $\vepsilon_t \sim N(\mathbf{0}, \mSigma_{\epsilon})$, we need to compute the E-FIM for the state vector $\valpha_t = [\vf_t', \vh_t']'$. This derivation follows the standard approach for Gaussian models where one set of parameters affects only the mean and another set affects only the covariance.

\subsubsection{The Log-Likelihood Function}

The log-likelihood for the observation model $\vr_t \sim N(\mLambda\vf_t, \boldsymbol{\Sigma}_t)$ is given by:

\begin{equation}
\log p(\vr_t \mid \valpha_{t}) = -\frac{1}{2}\left[\log|\boldsymbol{\Sigma}_t| + (\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t)\right].
\end{equation}

Where $\boldsymbol{\Sigma}_t = \mLambda\Diag(e^{\vh_t})\mLambda' + \mSigma_{\epsilon}$ is the marginal observation covariance matrix.

\subsubsection{Derivatives with respect to $\bm{f}_t$}

The factors $\vf_t$ affect only the mean of the observation distribution, not the covariance. The first derivative of the log-likelihood with respect to $\vf_t$ is:

\begin{equation}
\frac{\partial}{\partial \vf_t}\log p(\vr_t \mid \valpha_{t}) = \mLambda'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t).
\end{equation}

Taking the second derivative:

\begin{equation}
\frac{\partial^2}{\partial \vf_t\partial \vf_t'}\log p(\vr_t \mid \valpha_{t}) = -\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda.
\end{equation}

The E-FIM is the negative expectation of this Hessian:

\begin{equation}
\Infmat_{ff} = \mathbb{E}\left[-\frac{\partial^2}{\partial \vf_t\partial \vf_t'}\log p(\vr_t \mid \valpha_{t})\right] = \mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda.
\end{equation}

\subsubsection{Derivatives with respect to $\bm{h}_t$}

The log-volatilities $\vh_t$ affect only the covariance of the observation distribution, making their derivatives more complex than those for the factors. For the $(i,j)$-th element of the $\Infmat_{hh}$ block, we have:

\begin{equation}
[\Infmat_{hh}]_{i,j} = \mathbb{E}\left[-\frac{\partial^2}{\partial h_{i,t} \partial h_{j,t}}\log p(\vr_t \mid \valpha_{t})\right].
\end{equation}

To compute this, we need to differentiate both the log-determinant term and the quadratic form in the log-likelihood. Let's start with the first derivatives with respect to $h_{i,t}$:

\begin{align}
\frac{\partial}{\partial h_{i,t}}\log p(\vr_t \mid \valpha_{t}) &= -\frac{1}{2}\frac{\partial}{\partial h_{i,t}}\log|\boldsymbol{\Sigma}_t| - \frac{1}{2}\frac{\partial}{\partial h_{i,t}}\left[(\vr_t - \mLambda\bm{f}_t)'\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\bm{f}_t)\right] \\
&= -\frac{1}{2}\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) + \frac{1}{2}(\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t).
\end{align}

Where we've used the matrix calculus identities:
\begin{align}
\frac{\partial}{\partial h_{i,t}}\log|\boldsymbol{\Sigma}_t| &= \mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) \\
\frac{\partial}{\partial h_{i,t}}\left[\vx'\boldsymbol{\Sigma}_t^{-1}\vx\right] &= -\vx'\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\boldsymbol{\Sigma}_t^{-1}\vx.
\end{align}

The partial derivatives of the covariance matrix with respect to log-volatilities are:

\begin{equation}
\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}} = \mLambda\Diag(0,\ldots,e^{h_{i,t}},\ldots,0)\mLambda'.
\end{equation}

Where the diagonal matrix has $e^{h_{i,t}}$ in the $i$-th position and zeros elsewhere. This reflects how a change in log-volatility $h_{i,t}$ affects only the variance of the $i$-th factor.

Now, taking the second derivative with respect to $h_{j,t}$:

\begin{align}
\frac{\partial^2}{\partial h_{i,t}\partial h_{j,t}}\log p(\vr_t \mid \valpha_{t}) &= -\frac{1}{2}\frac{\partial}{\partial h_{j,t}}\text{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) \\
&+ \frac{1}{2}\frac{\partial}{\partial h_{j,t}}\left[(\vr_t - \mLambda\vf_t)'\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\boldsymbol{\Sigma}_t^{-1}(\vr_t - \mLambda\vf_t)\right].
\end{align}

The first term involves differentiating the trace expression, which yields:

\begin{align}
\frac{\partial}{\partial h_{j,t}}\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) &= \mathrm{tr}\left(\frac{\partial \boldsymbol{\Sigma}_t^{-1}}{\partial h_{j,t}}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) + \mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial^2 \boldsymbol{\Sigma}_t}{\partial h_{j,t}\partial h_{i,t}}\right) \\
&= -\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{j,t}}\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\right) + \mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial^2 \boldsymbol{\Sigma}_t}{\partial h_{j,t}\partial h_{i,t}}\right).
\end{align}

Where we've used the identity $\frac{\partial \boldsymbol{\Sigma}_t^{-1}}{\partial h_{j,t}} = -\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{j,t}}\boldsymbol{\Sigma}_t^{-1}$.

The second derivative of the covariance matrix is:

\begin{equation}
\frac{\partial^2 \boldsymbol{\Sigma}_t}{\partial h_{j,t}\partial h_{i,t}} = \begin{cases}
\mLambda\Diag(0,\ldots,e^{h_{i,t}},\ldots,0)\mLambda' & \text{if } i = j \\
\boldsymbol{0} & \text{if } i \neq j
\end{cases}.
\end{equation}

The second term in the Hessian involves differentiating a quadratic form, which is more complex. After taking the expectation, the terms involving $(\vr_t - \mLambda\vf_t)$ vanish because $\mathbb{E}[\vr_t - \mLambda\vf_t] = \mathbf{0}$.

The final result for the E-FIM is:

\begin{equation}
[\Infmat_{hh}]_{i,j} = \frac{1}{2}\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{i,t}}\boldsymbol{\Sigma}_t^{-1}\frac{\partial \boldsymbol{\Sigma}_t}{\partial h_{j,t}}\right).
\end{equation}

Substituting the expressions for the partial derivatives and working through the matrix algebra:

\begin{align}
[\Infmat_{hh}]_{i,j} &= \frac{1}{2}\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\mLambda\Diag(0,\ldots,e^{h_{i,t}},\ldots,0)\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda\Diag(0,\ldots,e^{h_{j,t}},\ldots,0)\mLambda'\right) \\
&= \frac{1}{2}e^{h_{i,t}}e^{h_{j,t}}\mathrm{tr}\left(\boldsymbol{\Sigma}_t^{-1}\mLambda\ve_i\ve_i'\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda\ve_j\ve_j'\mLambda'\right).
\end{align}

Where $\ve_i$ is the $i$-th standard basis vector (a vector with 1 in the $i$-th position and 0 elsewhere). Using properties of the trace and matrix multiplication, we can further simplify this expression:

\begin{align}
[\Infmat_{hh}]_{i,j} &= \frac{1}{2}e^{h_{i,t}}e^{h_{j,t}}\mathrm{tr}\left((\ve_i'\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda\ve_j)(\ve_j'\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda\ve_i)\right) \\
&= \frac{1}{2}e^{h_{i,t} + h_{j,t}}[\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda]_{i,j}^2.
\end{align}

Since $\Infmat_{ff} = \mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda$, we can express this more compactly as:

\begin{equation}
[\Infmat_{hh}]_{i,j} = \frac{1}{2}e^{h_{i,t} + h_{j,t}}[\Infmat_{ff}]_{i,j}^2.
\end{equation}

For $i, j = 1, \ldots, K$. This elegant result reveals a direct relationship between the FIM for log-volatilities and the FIM for factors, showing how the exponential nature of the volatility parameterization affects the curvature of the log-likelihood.

\subsubsection{Cross-Derivatives}

The cross-derivatives between $\vf_t$ and $\vh_t$ involve mixing derivatives of the mean and covariance terms. Since $\vf_t$ affects only the mean and $\vh_t$ affects only the covariance, these cross-terms involve expressions containing $(\vr_t - \mLambda\vf_t)$. When taking the expectation, since $\mathbb{E}[\vr_t - \mLambda\vf_t] = \mathbf{0}$, these terms vanish:

\begin{equation}
\Infmat_{fh} = \Infmat_{hf} = \mathbf{0}.
\end{equation}

\subsubsection{Block-Diagonal Structure}

Putting these results together, the E-FIM has a block-diagonal structure:

\begin{equation}
\Infmat_t = \begin{bmatrix}
\Infmat_{ff} & \boldsymbol{0} \\
\boldsymbol{0} & \Infmat_{hh}
\end{bmatrix} = \begin{bmatrix}
\mLambda'\boldsymbol{\Sigma}_t^{-1}\mLambda & \boldsymbol{0} \\
\boldsymbol{0} & \frac{1}{2}\left[e^{h_{i,t} + h_{j,t}}[\Infmat_{ff}]_{i,j}^2\right]_{i,j}
\end{bmatrix}.
\end{equation}

This block-diagonal structure is a direct consequence of the fact that in the DFSV model, the factors affect only the mean of the observation distribution, while the log-volatilities affect only the covariance. The elegant relationship between $\Infmat_{hh}$ and $\Infmat_{ff}$ allows for efficient computation, as $\Infmat_{hh}$ can be derived directly from $\Infmat_{ff}$ once it has been calculated for the factor update.

\subsection{Computational Efficiency Note}

The E-FIM can be computed efficiently by leveraging the Woodbury identity to avoid direct inversion of the $N \times N$ observation covariance matrix $\boldsymbol{\Sigma}_t$. This reduces computational complexity from $O(N^3)$ to $O(NK^2 + K^3)$, which is particularly important in high-dimensional settings where $N \gg K$. The elegant relationship between $\Infmat_{hh}$ and $\Infmat_{ff}$ allows for further efficiency, as $\Infmat_{hh}$ can be derived directly from $\Infmat_{ff}$ using the formula $[\Infmat_{hh}]_{i,j} = \frac{1}{2}e^{h_{i,t} + h_{j,t}}[\Infmat_{ff}]_{i,j}^2$.

\subsection{Significance of the FIM Derivatives}

The analytical derivation of the FIM derivatives with respect to log-volatilities $\vh_t$ is significant for both theoretical and practical reasons. The $\Infmat_{hh}$ block captures the curvature of the log-likelihood with respect to log-volatilities, providing accurate uncertainty estimates for volatility states. This is particularly important in financial applications where volatility estimation and its uncertainty are key for risk management.

The block-diagonal structure of the FIM supports the BCD approach by allowing separate updates for factors and log-volatilities, which is a key feature of the Bellman filter for DFSV models. Additionally, these derivatives are essential for parameter estimation via maximum likelihood, as they contribute to the score function and observed information matrix used in optimization algorithms.

\section{Optimizer Comparison for DFSV Models}\label{app:optimizer_comparison}

This section provides a detailed comparison of the optimization algorithms used for parameter estimation in the DFSV model. The choice of optimizer significantly impacts both the convergence reliability and the final parameter accuracy, particularly given the complex likelihood surface of DFSV models.

\subsection{Comprehensive Optimizer Comparison Results}

To provide a detailed view of optimizer performance, we present comprehensive comparison results for various optimization algorithms applied to the DFSV-BIF and DFSV-PF models with unfixed mean parameters ($\mu$). This comparison evaluates each optimizer based on convergence success, final loss value achieved, number of optimization steps required, and total computation time.

Table~\ref{tab:optimizer_comp_bif_unfixed} presents the results for the DFSV-BIF model.
\begin{table}
\centering
\caption{Optimizer Comparison Results (DFSV-BIF, Unfixed $\mu$ / Fix\_mu = No)}
\label{tab:optimizer_comp_bif_unfixed}
\begin{tabular}{lrrrr}
\toprule
              Optimizer & Success & Final Loss & Steps & Time (s) \\
\midrule
  DampedTrustRegionBFGS &     Yes &  -1,380.15 &   194 &    250.6 \\
IndirectTrustRegionBFGS &     Yes &  -1,380.15 &   174 &    227.5 \\
             DogLegBFGS &     Yes &  -1,380.15 &   367 &    482.6 \\
             ArmijoBFGS &     Yes &  -1,380.15 &   808 &    643.3 \\
                  AdamW &     Yes &  -1,380.15 &   991 &  1,474.8 \\
                   Adam &     Yes &  -1,380.15 &   992 &  1,375.7 \\
                RMSProp &     Yes &  -1,379.46 &   990 &  1,358.4 \\
                   Lion &     Yes &  -1,365.36 &   997 &  1,397.2 \\
              Adafactor &     Yes &  -1,352.82 &   972 &  1,362.8 \\
        GradientDescent &     Yes &  -1,349.00 &   983 &  1,297.0 \\
                   BFGS &     Yes &    -803.28 &   385 &    100.0 \\
                Adagrad &     Yes &   1,059.65 &   995 &  1,313.0 \\
               Adadelta &     Yes &  11,121.37 &   978 &  1,302.4 \\
                    SGD &      No &          - &     0 &  1,360.5 \\
\bottomrule
\end{tabular}
\end{table}

As shown in Table~\ref{tab:optimizer_comp_bif_unfixed}, most optimizers successfully converge, but with significant differences in efficiency and final loss values. The trust region variants of BFGS (DampedTrustRegionBFGS and IndirectTrustRegionBFGS) achieved the optimal final loss value with substantially fewer steps and lower computation time compared to gradient-based methods like Adam and AdamW. This supports the selection of DampedTrustRegionBFGS as the primary optimizer for the BIF approach in the main simulation studies. Standard SGD failed to converge, underscoring the necessity of adaptive step sizing for the complex likelihood surface of DFSV models.

Table~\ref{tab:optimizer_comp_pf_unfixed_revised} presents a similar comparison for the DFSV-PF model.
\begin{table}
\centering
\caption{Optimizer Comparison Results (DFSV-PF, Unfixed $\mu$ / Fix\_mu = No) - Revised}
\label{tab:optimizer_comp_pf_unfixed_revised}
\begin{tabular}{lrrrrp{4cm}}
\toprule
              Optimizer & Success & Final Loss & Steps & Time (s) &                                                            Notes \\
\midrule
                   BFGS &     Yes &  22,120.80 &   299 &    146.0 &                                                                - \\
  DampedTrustRegionBFGS &     Yes &  22,841.28 &    30 &     37.0 &                                                                - \\
             DogLegBFGS &     Yes &  22,841.28 &    52 &     65.3 &                                                                - \\
             ArmijoBFGS &     Yes &  22,841.28 &    81 &     63.1 &                                                                - \\
                  AdamW &      No &    Inf/NaN &     0 &  1,991.2 & The maximum number of steps was reached in the nonlinear solver. \\
IndirectTrustRegionBFGS &      No &    Inf/NaN &     0 &  1,474.3 & The maximum number of steps was reached in the nonlinear solver. \\
\bottomrule
\end{tabular}
\end{table}

Table~\ref{tab:optimizer_comp_pf_unfixed_revised} shows a different performance pattern for the DFSV-PF model. While several BFGS variants converged successfully, standard BFGS achieved the lowest final loss value, albeit requiring more steps than the trust region variants. Notably, AdamW and IndirectTrustRegionBFGS failed to converge, indicating the challenging nature of optimizing the particle filter likelihood. The ArmijoBFGS optimizer, selected for the PF approach, demonstrates a good balance of convergence reliability, final loss value, and computational efficiency among the tested methods that successfully converged.

\subsection{Selected Optimizer Algorithms}

Based on the comprehensive comparison results presented above, DampedTrustRegionBFGS was selected for the BIF approach and ArmijoBFGS was selected for the PF approach. The following paragraphs provide a detailed description of these two algorithms.

\paragraph{DampedTrustRegionBFGS:} This algorithm, used for the BIF approach, is a specialized variant of the BFGS quasi-Newton method that combines two key enhancements for robust optimization. First, it employs a damped Newton descent strategy that modifies the Hessian approximation to ensure positive definiteness, which is crucial for maintaining descent properties when optimizing non-convex functions. Second, it incorporates a classical trust region approach that adaptively adjusts step sizes based on the agreement between predicted and actual objective function improvements.

The trust region mechanism uses configurable high and low cutoff thresholds (0.995 and 0.1 by default) with corresponding expansion and contraction constants (2.5 and 0.2) to dynamically resize the trust region during optimization. When the actual improvement closely matches or exceeds the predicted improvement (ratio $>$ 0.995), the trust region expands by a factor of 2.5, allowing for more aggressive steps. Conversely, when the actual improvement falls significantly short of predictions (ratio $<$ 0.1), the trust region contracts by a factor of 0.2, forcing more conservative steps. This adaptive approach is particularly effective for the non-convex likelihood surfaces encountered in DFSV models with stochastic volatility components.

\paragraph{ArmijoBFGS:} This algorithm, used for the PF approach, combines BFGS with a backtracking Armijo line search strategy. The line search begins with an initial step size (0.1 by default) and progressively reduces it until the Armijo condition is satisfied, which ensures sufficient decrease in the objective function relative to the gradient magnitude. This approach is particularly well-suited for the particle filter likelihood, which can exhibit irregularities due to the Monte Carlo approximation of the likelihood function.

The Armijo condition specifically requires that the actual decrease in the objective function is at least a fraction (typically 0.0001) of the decrease predicted by a linear approximation. This conservative approach helps prevent steps that might lead to numerical instabilities or divergence, which is especially important when optimizing the particle filter likelihood where the gradient information may be noisy due to the stochastic nature of the particle approximation.
\chapter{Supplementary Data Analysis}\label{app:data_supplementary}

This appendix provides additional details and visualizations related to the exploratory data analysis presented in Chapter~\ref{ch:data_evaluation_simulation}.

\section{Detailed Portfolio Return Statistics}\label{app:detailed_return_stats}

% Moved from data.tex Section 3.2.1
\begin{table}[htbp]
  \centering
  \caption{Concise Summary Statistics for Demeaned Portfolio Returns ($\vr_t'$)}
  \label{tab:summary_stats_returns_concise_appendix} % Updated label
  \footnotesize
  \setlength{\tabcolsep}{4pt}
  \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}lrrrr@{}}
  \toprule
  Statistic & SMALL LoBM & ME5 BM5 & BIG LoBM & Avg\_Return \\
  \midrule
  Mean & 0.000 & 0.000 & 0.000 & 0.000 \\
  Std Dev & 0.085 & 0.058 & 0.049 & 0.053 \\
  Skewness & 0.229 & -0.416 & -0.118 & -0.488 \\
  Kurtosis & 2.592 & 1.633 & 1.100 & 2.335 \\
  \bottomrule
  \end{tabular*}
  \normalsize
\end{table}

% The original detailed table follows:
\begin{table}[htbp]
  \caption{Summary Statistics for Demeaned Portfolio Returns ($\vr_t'$)}
  \label{tab:summary_stats_returns_appendix}
  \footnotesize
  \begin{tabularx}{\textwidth}{@{}lXXXXXXXX@{}}
  \toprule
  Statistic & SMALL LoBM & ME1 BM2 & SMALL HiBM & ME5 BM5 & BIG LoBM & ME9 BM2 & ME10 BM2 & Avg\_Return \\
  \midrule
  Mean & 0.000 & 0.000 & 0.000 & 0.000 & 0.000 & 0.000 & 0.000 & 0.000 \\
  Std Dev & 0.085 & 0.080 & 0.065 & 0.058 & 0.049 & 0.052 & 0.046 & 0.053 \\
  Skewness & 0.229 & 0.201 & -0.006 & -0.416 & -0.118 & -0.263 & -0.297 & -0.488 \\
  Kurtosis & 2.592 & 2.126 & 3.510 & 1.633 & 1.100 & 1.702 & 1.532 & 2.335 \\
  Minimum & -0.346 & -0.324 & -0.305 & -0.268 & -0.195 & -0.256 & -0.249 & -0.277 \\
  Maximum & 0.441 & 0.363 & 0.337 & 0.249 & 0.215 & 0.216 & 0.195 & 0.226 \\
  Autocorr(1) & 0.211 & 0.182 & 0.222 & 0.057 & 0.025 & 0.027 & 0.009 & 0.101 \\
  \bottomrule
  \end{tabularx}
  \normalsize
\end{table}

\begin{figure}[htbp]
  \centering
  \caption{Time Series Plots of Selected Demeaned Portfolio Returns}
  \label{fig:ts_plots_returns_appendix}
  \includegraphics[width=0.8\textwidth]{eda/figure1_returns_timeseries.png}
\end{figure}

\section{Portfolio Characteristics Analysis}\label{app:portfolio_characteristics}

\begin{table}[htbp]
  \caption{Summary Statistics for Portfolio Characteristics (Number of Firms, Average Market Cap)}
  \label{tab:summary_stats_chars_appendix}
  \footnotesize
  \begin{tabularx}{\textwidth}{@{}lXX@{}}
  \toprule
  Portfolio & Mean Number of Firms & Mean Market Cap (\$ Millions) \\
  \midrule
  Average & 38.6 & 4,513.4 \\
  SMALL LoBM & 208.0 & 53.5 \\
  ME1 BM2 & 115.0 & 55.9 \\
  SMALL HiBM & 356.4 & 38.8 \\
  ME5 BM5 & 22.1 & 947.0 \\
  BIG LoBM & 37.8 & 50,947.3 \\
  ME9 BM2 & 22.5 & 7,399.3 \\
  ME10 BM2 & 26.3 & 41,880.2 \\
  \bottomrule
  \end{tabularx}
  \normalsize
\end{table}

\begin{figure}[htbp]
  \centering
  \caption{Time Series of Average Portfolio Characteristics}
  \label{fig:avg_chars_ts_appendix}
  \includegraphics[width=\textwidth]{eda/figure3_avg_characteristics_timeseries.png}
\end{figure}


\begin{figure}[htbp]
  \centering
  \caption{Average Market Capitalization by Size Decile}
  \label{fig:avg_mktcap_size_appendix}
  \includegraphics[width=0.7\textwidth]{eda/figure4_avg_mkt_cap_by_size.png}
\end{figure}

% Section removed and content moved to data.tex, Section 3.3 (sec:pca_factor_selection) on 2025-04-30


\chapter{Supplementary material for simulation studies}

\section{Simulation Study 1: Parameter Generation Details}\label{app:sim1_params}

Table~\ref{tab:dgp_parameters_study1_appendix} details the parameter generation process for Simulation Study 1, focusing on computational scaling.

\begin{table}[htbp]
    \centering
    \caption{Parameter Generation for Simulation Study 1 (Computational Scaling)}
    \label{tab:dgp_parameters_study1_appendix}
    \footnotesize
    \setlength{\tabcolsep}{3pt}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{3.5cm}p{2.5cm}p{4.5cm}p{3.5cm}@{}}
    \toprule
    \textbf{Parameter} & \textbf{Dimensions} & \textbf{Distribution/Method} & \textbf{Constraints} \\
    \midrule
    Factor loadings ($\mLambda$)       & $N \times K$ & $\mathcal{N}(0, 1)$                                       & None \\
    Factor transition ($\mPhi_f$)     & $K \times K$ & $\mathcal{N}(0, 0.5)$                                     & $|\text{eig}(\mPhi_f)|< 1$ \\
    Log-volatility transition ($\mPhi_h$) & $K \times K$ & $\mathcal{N}(0, 0.5)$                                     & $|\text{eig}(\mPhi_h)|< 1$ \\
    Log-volatility mean ($\vmu$)        & $K$ & $\mathcal{N}(-1, 0.5)$                                    & None \\
    Idiosyncratic variance ($\sigma^2_\epsilon$) & $N$ (diagonal) & $\exp(z)$ where $z \sim \mathcal{N}(-1, 0.5)$          & Positive by construction \\
    Log-volatility innovation ($\mQ_h$) & $K \times K$ & $\mQ_{raw} \sim \mathcal{N}(0, 0.2)$, $\mQ_h = \mQ_{raw}\mQ_{raw}^\top$ & PD by construction \\
    \bottomrule
    \end{tabular*}
    \normalsize
\end{table}

This section provides supplementary material for Simulation Study 1, discussed in Section~\ref{ch:simulation}. It includes detailed stability analysis tables and additional figures illustrating performance metrics across various configurations.

\subsection{Stability Analysis Tables}
\label{app:stability_tables}
Tables \ref{tab:bif_stability} and \ref{tab:pf_stability} summarize the stability analysis for the BIF and PF, respectively, based on the mean-to-median RMSE ratios. Ratios significantly greater than 1 indicate potential instability or skewed error distributions.

% BIF Stability Table
\begin{table}
\caption{BIF Stability Analysis: Mean vs Median RMSE (Top 10 by Factor Ratio).}
\label{tab:bif_stability}
\begin{tabularx}{\textwidth}{@{}llXXXXXX@{}}
\toprule
N & K & Mean RMSE (f) & Median RMSE (f) & Ratio (f) & Mean RMSE (h) & Median RMSE (h) & Ratio (h) \\
\midrule
20 & 5 & 5.19e+00 & 4.72e-01 & 10.99 & 9.34e-01 & 8.56e-01 & 1.09 \\
150 & 15 & 9.75e+00 & 1.14e+00 & 8.57 & 2.27e+00 & 1.36e+00 & 1.67 \\
100 & 10 & 3.53e+00 & 8.53e-01 & 4.13 & 1.27e+00 & 1.16e+00 & 1.09 \\
50 & 15 & 4.68e+00 & 1.22e+00 & 3.84 & 6.67e+00 & 1.30e+00 & 5.14 \\
50 & 10 & 3.10e+00 & 8.42e-01 & 3.68 & 1.27e+00 & 1.19e+00 & 1.07 \\
20 & 15 & 4.74e+00 & 1.41e+00 & 3.37 & 1.62e+00 & 1.28e+00 & 1.26 \\
10 & 10 & 3.30e+00 & 1.04e+00 & 3.18 & 1.30e+00 & 1.18e+00 & 1.11 \\
150 & 10 & 1.90e+00 & 7.22e-01 & 2.63 & 1.26e+00 & 1.15e+00 & 1.09 \\
20 & 10 & 1.47e+00 & 7.33e-01 & 2.01 & 1.11e+00 & 1.04e+00 & 1.06 \\
5 & 5 & 1.03e+00 & 5.93e-01 & 1.74 & 8.60e-01 & 8.09e-01 & 1.06 \\
\bottomrule
\end{tabularx}
\end{table}

% PF Stability Table
\begin{table}
\caption{PF Stability Analysis: Mean vs Median RMSE (Top 10 by Factor Ratio).}
\label{tab:pf_stability}
\begin{tabularx}{\textwidth}{@{}llXXXXXXX@{}}
\toprule
N & K & Particles & Mean RMSE (f) & Median RMSE (f) & Ratio (f) & Mean RMSE (h) & Median RMSE (h) & Ratio (h) \\
\midrule
50 & 10 & 5000 & 4.02e+12 & 9.44e-01 & 4.26e+12 & 2.06e+00 & 1.69e+00 & 1.21 \\
100 & 5 & 20000 & 5.61e+07 & 2.05e-01 & 2.73e+08 & 1.16e+00 & 9.33e-01 & 1.24 \\
50 & 10 & 20000 & 9.27e+06 & 6.43e-01 & 1.44e+07 & 1.79e+00 & 1.46e+00 & 1.22 \\
20 & 15 & 1000 & 2.18e+06 & 2.30e+00 & 9.51e+05 & 2.63e+00 & 2.39e+00 & 1.10 \\
10 & 5 & 5000 & 9.45e+03 & 3.33e-01 & 2.84e+04 & 1.05e+00 & 8.51e-01 & 1.23 \\
100 & 15 & 1000 & 1.93e+04 & 1.55e+00 & 1.25e+04 & 2.29e+00 & 2.10e+00 & 1.09 \\
50 & 15 & 1000 & 1.62e+04 & 1.84e+00 & 8.83e+03 & 2.52e+00 & 2.19e+00 & 1.15 \\
10 & 10 & 20000 & 4.22e+03 & 8.92e-01 & 4.73e+03 & 1.85e+00 & 1.56e+00 & 1.18 \\
100 & 10 & 1000 & 2.37e+03 & 9.32e-01 & 2.54e+03 & 2.02e+00 & 1.70e+00 & 1.19 \\
150 & 10 & 1000 & 1.75e+03 & 1.00e+00 & 1.74e+03 & 2.00e+00 & 1.73e+00 & 1.16 \\
\bottomrule
\end{tabularx}
\end{table}

The stability analysis reveals striking differences between the BIF and PF approaches. While the BIF exhibits moderate factor ratio instability (maximum 10.99), the PF shows extreme instability with factor ratios reaching orders of magnitude higher (up to 4.26e+12). This indicates that the PF occasionally produces catastrophic estimation failures that dramatically skew the mean RMSE upward, despite having comparable or even better median performance in some configurations. Interestingly, both methods show relatively stable log-volatility estimation (ratios mostly between 1.0-1.7), suggesting that volatility estimates are more robust than factor estimates across filtering approaches. The BIF's deterministic nature provides more consistent performance across replications, while the PF's stochastic approach leads to occasional but severe estimation failures, particularly in higher-dimensional settings ($N\geq 50$, $K\geq 10$) or with insufficient particles. These findings highlight the importance of considering not just median performance but also stability when selecting a filtering approach for practical applications.

\subsection{Supplementary Figures}

The following figures provide additional visualizations of the performance metrics discussed in the main text, organized to tell a comprehensive story about filter performance across different dimensions and metrics.

\subsubsection{Heatmap Visualizations: The Complete Dimensional Landscape}

While the main text presents selected heatmaps, the complete set below offers a more comprehensive view of how both filters perform across the entire dimensional grid. These visualizations reveal patterns that might be missed when examining individual slices of the parameter space.

% Heatmaps
\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/heatmap_corr_f_bif.png}
    \caption{Heatmap of BIF Median Factor Correlation vs. N and K. The relatively uniform coloring across most of the grid indicates that the BIF maintains consistent factor estimation quality even as dimensions increase, with only slight degradation in the highest-dimensional corner (N=150, K=15). This stability is a key advantage of the deterministic BIF approach.}
    \label{fig:app:heatmap_corr_f_bif}
\end{figure}

\begin{figure}[htbp]
  \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/heatmap_corr_f_pf20000.png}
    \caption{Heatmap of PF-20k Median Factor Correlation vs. N and K. Compared to the BIF heatmap in Figure~\ref{fig:app:heatmap_corr_f_bif}, the PF shows stronger performance (lighter colors) in the lower-dimensional regions but experiences more pronounced degradation as K increases. This illustrates the PF's strength in lower dimensions and its challenges in higher-dimensional state-spaces, even with 20,000 particles.}
    \label{fig:app:heatmap_corr_f_pf}
\end{figure}

\section{Simulation Study 2: Parameter Generation Details}\label{app:sim2_params}

Table~\ref{tab:dgp_parameters_study2_appendix} details the parameter generation process for Simulation Study 2, focusing on parameter estimation performance.

\begin{table}[htbp]
    \centering
    \caption{Parameter Generation for Simulation Study 2 (Parameter Estimation)}
    \label{tab:dgp_parameters_study2_appendix} % Updated label
    \footnotesize
    \setlength{\tabcolsep}{3pt}
    \begin{tabularx}{\textwidth}{p{3.5cm}p{2.5cm}XX}
    \toprule
    \textbf{Parameter} & \textbf{Dimensions} & \textbf{Distribution/Method} & \textbf{Constraints} \\
    \midrule
    Factor loadings ($\mLambda$) & $N \times K$ & $\mathcal{N}(0.5, 0.5)$ & Lower triangular with diagonal elements fixed to $1.0$ for identification. \\
    Factor transition ($\mPhi_f$) & $K \times K$ & Off-diagonals $\sim \mathcal{U}(0.01, 0.1)$, diagonals $\sim \mathcal{U}(0.15, 0.35)$ & Stationarity ensured (stable by construction). \\
    Log-volatility transition ($\mPhi_h$) & $K \times K$ & Off-diagonals $\sim \mathcal{U}(0.01, 0.1)$, diagonals $\sim \mathcal{U}(0.9, 0.99)$ & Stationarity ensured (spectral norm $< 0.97$). \\
    Log-volatility mean ($\vmu$) & $K$ & Fixed values, e.g., $[-1.0, -0.5]$ for $K=2$ or $[-1.0, \ldots, -1.0]$ for $K>2$. & None. \\
    Idiosyncratic variance ($\sigma^2_\epsilon$) & $N$ (diagonal) & $\mathcal{U}(0.05, 0.1)$ & Positive by construction. \\
    Log-volatility innovation ($\mQ_h$) & $K$ (diagonal) &  $\mathcal{U}(0.8, 1.0)$ & Positive by construction. \\
    \bottomrule
    \end{tabularx}
    \normalsize
\end{table}



\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/heatmap_rmse_h_pf20000.png}
    \caption{Heatmap of PF-20k Median Log-Volatility RMSE vs. N and K. The darker regions in the upper-right corner reveal the PF's difficulty in accurately estimating log-volatilities as both N and K increase. When compared with the BIF's log-volatility RMSE heatmap in Figure~\ref{fig:sim1:heatmap_rmse_h_bif} of the main text, this visualization highlights one of the BIF's key advantages: more robust log-volatility estimation in higher dimensions.}
    \label{fig:app:heatmap_rmse_h_pf}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/heatmap_time_pf20000.png}
    \caption{Heatmap of PF-20k Median Computation Time (s) vs. N and K. The gradual darkening from left to right and bottom to top illustrates the PF's computational scaling with both N and K. Unlike the BIF's heatmap in Figure~\ref{fig:sim1:heatmap_time_bif} of the main text, which shows more dramatic darkening along the K axis, the PF exhibits more balanced scaling with both dimensions, confirming its theoretical $O(NK)$ complexity. This different scaling behavior is a key consideration when choosing between filters for specific dimensional configurations.}
    \label{fig:app:heatmap_time_pf}
\end{figure}

\subsubsection{Unique Supplementary Visualizations}

While the main text presents the key performance metrics with detailed visualizations, the appendix focuses on complementary analyses that provide additional insights not covered in the main text. The following figures offer unique perspectives on filter performance that help complete the overall evaluation picture.

\paragraph{Stability and Trade-off Analysis:}

The final set of figures moves beyond individual metrics to examine broader patterns: the numerical stability of each filter and the fundamental trade-offs between computational cost and estimation accuracy.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/stability_ratio_f_vs_K_N50_log.png}
    \caption{Mean/Median Factor RMSE Ratio vs. K (N=50, Log Scale). This figure provides a direct measure of filter stability by showing the ratio of mean to median RMSE. Values close to 1 indicate consistent performance across replications, while higher values suggest occasional poor performance (outliers) that skews the mean upward. The BIF maintains a ratio near 1 across all K values, reflecting its deterministic nature and consistent performance. In contrast, the PF variants show increasing ratios with K, particularly for lower particle counts, indicating growing instability as the state dimension increases. This instability is a key practical limitation of the PF approach that is not captured by median metrics alone.}
    \label{fig:app:stability_ratio}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\textwidth]{simulation1_analysis/figures/tradeoff_rmse_h_vs_time.png}
    \caption{Trade-off between Median Log-Volatility RMSE and Median Computation Time. This figure synthesizes the key performance dimensions into a single visualization, plotting accuracy (log-volatility RMSE) against computational cost for different filter configurations and model dimensions. Points toward the bottom-left corner represent the ideal combination of low error and fast runtime.}
    \label{fig:app:tradeoff_rmse_time}
\end{figure}

\paragraph{Summary of Supplementary Analysis:}
Taken together, these supplementary figures tell a nuanced story about the relative strengths of the BIF and PF approaches. The BIF offers superior log-volatility estimation, more consistent performance across replications, and favorable accuracy-to-computation trade-offs for most configurations. The PF provides slightly better factor estimation in lower dimensions but struggles with stability and computational scaling as dimensions increase. These findings complement and extend the analysis in the main text, providing a more complete picture of how these filtering approaches perform across the full range of tested configurations.

\section{Supplementary Material for Simulation Study 2: Estimation Algorithm Details}\label{app:estimation_algorithm_details}

This section provides detailed information on the optimization algorithms and configurations used for parameter estimation in Simulation Study 2, complementing the concise description in Section~\ref{sec:simulation_design:study2_estimation:filter_estimation_config}.

Parameter estimation is performed via maximum likelihood using both BIF and PF.

\subsection{Selected Estimation Algorithms}

Based on preliminary studies (see Appendix~\ref{app:optimizer_comparison}), DampedTrustRegionBFGS was selected for the BIF approach and ArmijoBFGS was selected for the PF approach due to their convergence reliability and accuracy on the respective likelihood surfaces.

\paragraph{DampedTrustRegionBFGS (for BIF):} This algorithm is a specialized variant of the BFGS quasi-Newton method that combines a damped Newton descent strategy with a classical trust region approach. The damped Newton descent modifies the Hessian approximation to ensure positive definiteness, crucial for optimizing non-convex functions. The trust region approach adaptively adjusts step sizes based on the agreement between predicted and actual objective function improvements. The trust region mechanism uses configurable high and low cutoff thresholds (0.995 and 0.1 by default) with corresponding expansion and contraction constants (2.5 and 0.2) to dynamically resize the trust region during optimization. This adaptive approach is particularly effective for the non-convex pseudo-likelihood surfaces encountered in DFSV models.

\paragraph{ArmijoBFGS (for PF):} This algorithm combines BFGS with a backtracking Armijo line search strategy. The line search begins with an initial step size (0.1) and progressively reduces it until the Armijo condition is satisfied, ensuring sufficient decrease in the objective function relative to the gradient magnitude. This strategy helps navigate the potentially rugged likelihood landscapes that arise from the particle-based approximation of the likelihood function.

\subsection{Estimation Configurations}

Both optimizers were implemented using the Optimistix library \cite{rader_optimistix_2024}. Parameter transformations (e.g., tanh for diagonals, softplus for variances) and eigenvalue penalties ($\lambda = 10^4$) were employed to enforce stability constraints. Optimizations were run for a maximum of 2000 iterations.

For the PF approach, two particle counts are used: PF-1000 ($P=1000$) and PF-5000 ($P=5000$). The primary BIF variant estimates all parameters, including $\vmu$.

Optimizations are initialized with plausible parameter values generated similarly to the true parameters but with different random seeds, simulating a realistic scenario without prior knowledge of the true values.
\section{Simulation Study 2: Supplementary Material (Bias Analysis)}\label{app:sim2_bias_appendix}

This section provides a comprehensive analysis of parameter estimation bias patterns observed in Simulation Study 2, expanding on the discussion in Section~\ref{sec:sim2:bias} of the main text. While the main text focuses specifically on the bias patterns for the log-volatility transition matrix $\mPhi_h$, this appendix presents bias versus RMSE scatter plots for all model parameters across different model configurations.

\subsection{Bias Analysis Methodology}

For each parameter and model configuration, we analyze two key metrics:
\begin{itemize}
    \item \textbf{Bias}: The average difference between estimated and true parameter values (Est - True), capturing systematic estimation errors.
    \item \textbf{RMSE}: The root mean squared error, capturing the overall accuracy of parameter estimates by combining both bias and variance.
\end{itemize}

The scatter plots presented in this section visualize the relationship between these metrics across different filter configurations (BIF, PF with varying particle counts) and time series lengths. Each point represents a specific simulation run, with marker shape indicating time series length and color indicating filter configuration. This visualization approach allows us to identify patterns in the bias-variance trade-off for different parameters and filtering approaches.

\subsection{Factor Loading Parameters ($\mLambda$)}

Figure~\ref{fig:app:bias_lambda_r} presents the bias versus RMSE scatter plots for factor loading parameters across different model configurations. These parameters determine how the latent factors influence the observed returns.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_lambda_r_comparison.pdf}
    \caption{Bias versus RMSE for factor loading parameters ($\mLambda$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_lambda_r}
\end{figure}

The scatter plots reveal that BIF estimates (blue crosses/triangles) typically exhibit lower RMSE compared to PF estimates, particularly for larger model dimensions. For all configurations, they demonstrate higher variance and bias, resulting in larger overall estimation errors. This pattern suggests that the BIF's deterministic approach provides more consistent factor loading estimates, even if they occasionally show small systematic biases.

\subsection{Factor Transition Matrix ($\mPhi_f$)}

Figure~\ref{fig:app:bias_Phi_f} shows the bias versus RMSE scatter plots for the factor transition matrix parameters, which govern the temporal dynamics of the latent factors.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_Phi_f_comparison.pdf}
    \caption{Bias versus RMSE for factor transition matrix parameters ($\mPhi_f$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_Phi_f}
\end{figure}

For the factor transition matrix, both BIF and PF approaches show relatively similar performance patterns, with the PF-10K having superior performance in terms of RMSE for the smaller model configurations. The bias patterns are more mixed compared to other parameters, suggesting that the temporal dynamics of factors present similar estimation challenges for both filtering approaches.

\subsection{Log-Volatility Transition Matrix ($\mPhi_h$)}

Figure~\ref{fig:app:bias_Phi_h} presents the bias versus RMSE scatter plots for the log-volatility transition matrix parameters, which was highlighted in the main text as showing a distinctive bias pattern for the BIF.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_Phi_h_comparison.pdf}
    \caption{Bias versus RMSE for log-volatility transition matrix parameters ($\mPhi_h$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_Phi_h}
\end{figure}

As discussed in the main text, the BIF estimates for $\mPhi_h$ show a consistent small negative bias across configurations, indicating a tendency to slightly underestimate the persistence of log-volatilities. However, the BIF achieves substantially lower RMSE compared to PF variants, demonstrating a favorable bias-variance trade-off. The PF estimates show wider dispersion around zero bias, with higher overall estimation errors, particularly for configurations with fewer particles.

\subsection{Volatility Covariance Matrix ($\mQ_h$)}

Figure~\ref{fig:app:bias_Q_h} shows the bias versus RMSE scatter plots for the volatility covariance matrix parameters, which determine the magnitude and correlation structure of log-volatility innovations.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_Q_h_comparison.pdf}
    \caption{Bias versus RMSE for volatility covariance matrix parameters ($\mQ_h$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_Q_h}
\end{figure}

The volatility covariance matrix parameters show some of the most pronounced differences between filtering approaches. The PF estimates consistently achieve lower RMSE and a smaller bias compared to the BIF. The BIF estimates show a consistent negative bias pattern, suggesting a tendency to underestimate the volatility of log-volatility processes. This underestimation may be related to the BIF's mode-seeking nature, which can lead to smoother volatility estimates compared to the full posterior representation of the PF.

\subsection{Long-run Mean of Log-Volatilities ($\vmu$)}

Figure~\ref{fig:app:bias_mu} presents the bias versus RMSE scatter plots for the long-run mean parameters of the log-volatilities, which determine the central tendency of the volatility processes.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_mu_comparison.pdf}
    \caption{Bias versus RMSE for long-run mean parameters of log-volatilities ($\vmu$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_mu}
\end{figure}

The long-run mean parameters show some of the most challenging estimation patterns among all parameters, with relatively high RMSE for both filtering approaches. This difficulty aligns with the parameter difficulty ranking presented in Table~\ref{tab:sim2:param_median_rmse_bias} of the main text, where $\vmu$ components are among the most difficult to estimate accurately. The various PF configurations appear to be more consistent in their estimation of $\vmu$ compared to the BIF.

\subsection{Idiosyncratic Noise Variance ($\mSigma_\epsilon$)}

Figure~\ref{fig:app:bias_sigma2} shows the bias versus RMSE scatter plots for the idiosyncratic noise variance parameters, which determine the magnitude of asset-specific return variations not explained by the common factors.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/scatter_sigma2_comparison.pdf}
    \caption{Bias versus RMSE for idiosyncratic noise variance parameters ($\mSigma_\epsilon$) across different model configurations. Each panel represents a specific $(N,K)$ pair, with marker shape indicating time series length and color indicating filter configuration.}
    \label{fig:app:bias_sigma2}
\end{figure}

For the smaller model configurations both filtering approaches show relatively similar performance patterns, with the BIF generally achieving slightly lower RMSE. As the model dimensions increase, the PF estimates show significantly increased RMSE and bias, particularly for configurations with fewer particles.

\subsection{Synthesis of Bias Analysis Findings}

The comprehensive bias analysis presented in this appendix reveals several important patterns:

\begin{enumerate}
    \item \textbf{Bias-Variance Trade-off}: The BIF consistently demonstrates lower overall estimation error (RMSE) across most parameters and configurations, but often shows small systematic biases. In contrast, PF estimates typically cluster around zero bias but exhibit higher variance, resulting in larger overall estimation errors.

    \item \textbf{Parameter-Specific Patterns}: Different parameters show distinct bias patterns. The log-volatility parameters ($\mQ_h$, $\vmu$) show the most pronounced differences between filtering approaches, while factor-related parameters ($\mLambda$, $\mPhi_f$) and observation parameters ($\mSigma_\epsilon$) show more similar patterns.

    \item \textbf{Dimensional Effects}: As model dimensions increase (higher $N$ and $K$), the performance gap between BIF and PF approaches generally widens, with the BIF maintaining more consistent estimation quality.

    \item \textbf{Time Series Length Effects}: Longer time series (T=2000 vs. T=1000) generally lead to improved estimation accuracy for both approaches, but the relative patterns between filtering methods remain consistent.
\end{enumerate}

These findings extend the discussion in the main text by providing a more comprehensive view of parameter estimation patterns across the full DFSV model specification. The observed bias-variance trade-off between BIF and PF approaches represents an important consideration for practitioners when choosing between these filtering methods for empirical applications.
\subsection{Parameter Estimation Error Scaling}
\label{app:sim2:param_scaling}

This section presents additional figures illustrating how the median Root Mean Squared Error (RMSE) for various model parameters scales with model dimensions ($N$, $K$, and $T$) in Simulation Study 2. These figures complement Figure~\ref{fig:sim2:param_error_scaling} in the main text and provide a more complete picture of parameter estimation accuracy across the full set of parameters.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_mu_rmse.pdf}
    \caption{Median RMSE for log-volatility long-run mean parameters ($\vmu$) scaling with model dimensions ($N, K, T$). BIF and PF variants show relatively high RMSE, indicating difficulty in estimating this parameter, with performance generally improving with increasing $T$.}
    \label{fig:app:sim2:rmse_scaling_mu}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_Phi_f_rmse.pdf}
    \caption{Median RMSE for factor transition matrix parameters ($\mPhi_f$) scaling with model dimensions ($N, K, T$). BIF generally achieves lower RMSE than PF variants, with accuracy improving with increasing $T$.}
    \label{fig:app:sim2:rmse_scaling_phif}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_Q_h_rmse.pdf}
    \caption{Median RMSE for log-volatility innovation covariance parameters ($\mQ_h$) scaling with model dimensions ($N, K, T$). PF-10k shows the lowest RMSE for this parameter, while BIF exhibits higher RMSE, particularly with increasing $K$.}
    \label{fig:app:sim2:rmse_scaling_qh}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_sigma2_rmse.pdf}
    \caption{Median RMSE for idiosyncratic noise variance parameters ($\mSigma_\epsilon$) scaling with model dimensions ($N, K, T$). Both BIF and PF variants show similar RMSE patterns, with accuracy improving with increasing $T$.}
    \label{fig:app:sim2:rmse_scaling_sigma2}
\end{figure}

\chapter{Supplementary Material for Empirical Application}
\label{app:empirical_supplementary}

This appendix provides supplementary figures and detailed results for the empirical application discussed in Chapter~\ref{ch:empirical_application}. It includes individual factor and log-volatility plots that complement the aggregated figures presented in the main text.

\section{Individual Factor Estimates}
\label{app:individual_factors}

This section presents the individual factor estimates from both the DFSV-BIF and DFSV-PF models. These detailed plots complement the aggregated overviews presented in the main text.

\subsection{BIF Factor Estimates}
\label{app:bif_factors}

The following figures show the individual factor estimates from the DFSV-BIF model. These detailed plots complement the overview presented in Figure~\ref{fig:empirical_factors_bif} of the main text.

\begin{figure}[htbp]
  \centering
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/factor_1.png}
    \caption{Factor 1}
    \label{fig:app_empirical_factors_bif_f1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/factor_2.png}
    \caption{Factor 2}
    \label{fig:app_empirical_factors_bif_f2}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/factor_3.png}
    \caption{Factor 3}
    \label{fig:app_empirical_factors_bif_f3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/factor_4.png}
    \caption{Factor 4}
    \label{fig:app_empirical_factors_bif_f4}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/factor_5.png}
    \caption{Factor 5}
    \label{fig:app_empirical_factors_bif_f5}
  \end{subfigure}
  \hfill % Add emptyhfill to balance the last row
  \begin{subfigure}[b]{0.48\textwidth}
    % Empty subfigure to balance the grid
  \end{subfigure}

  \caption{Individual Estimated Latent Factors ($\hat{\vf}_{t|T}$) (DFSV-BIF)}
  \label{fig:app_empirical_factors_bif}
  \par\medskip \footnotesize Note: Smoothed estimates of the K=5 latent factors obtained from the DFSV-BIF model over the full sample period (July 1963–Dec 2023).
\end{figure}

\subsection{PF Factor Estimates}
\label{app:pf_factors}

The following figures show the individual factor estimates from the DFSV-PF model. These detailed plots provide a comparison with the BIF estimates and complement the overview presented in the main text.

\begin{figure}[htbp]
  \centering
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/factor_1.png}
    \caption{Factor 1}
    \label{fig:app_empirical_factors_pf_f1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/factor_2.png}
    \caption{Factor 2}
    \label{fig:app_empirical_factors_pf_f2}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/factor_3.png}
    \caption{Factor 3}
    \label{fig:app_empirical_factors_pf_f3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/factor_4.png}
    \caption{Factor 4}
    \label{fig:app_empirical_factors_pf_f4}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/factor_5.png}
    \caption{Factor 5}
    \label{fig:app_empirical_factors_pf_f5}
  \end{subfigure}
  \hfill % Add emptyhfill to balance the last row
  \begin{subfigure}[b]{0.48\textwidth}
    % Empty subfigure to balance the grid
  \end{subfigure}

  \caption{Individual Estimated Latent Factors ($\hat{\vf}_{t|T}$) (DFSV-PF)}
  \label{fig:app_empirical_factors_pf}
  \par\medskip \footnotesize Note: Smoothed estimates of the K=5 latent factors obtained from the DFSV-PF model over the full sample period (July 1963–Dec 2023).
\end{figure}

\section{Individual Log-Volatility Estimates}
\label{app:individual_logvols}

This section presents the individual log-volatility estimates from both the DFSV-BIF and DFSV-PF models. These detailed plots complement the aggregated overviews presented in the main text.

\subsection{BIF Log-Volatility Estimates}
\label{app:bif_logvols}

The following figures show the individual log-volatility estimates from the DFSV-BIF model. These detailed plots complement the overview presented in Figure~\ref{fig:empirical_factors_bif} of the main text.

\begin{figure}[htbp]
  \centering
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/logvol_1.png}
    \caption{Log-Volatility Factor 1}
    \label{fig:app_empirical_logvols_bif_h1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/logvol_2.png}
    \caption{Log-Volatility Factor 2}
    \label{fig:app_empirical_logvols_bif_h2}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/logvol_3.png}
    \caption{Log-Volatility Factor 3}
    \label{fig:app_empirical_logvols_bif_h3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/logvol_4.png}
    \caption{Log-Volatility Factor 4}
    \label{fig:app_empirical_logvols_bif_h4}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/logvol_5.png}
    \caption{Log-Volatility Factor 5}
    \label{fig:app_empirical_logvols_bif_h5}
  \end{subfigure}
  \hfill % Add emptyhfill to balance the last row
  \begin{subfigure}[b]{0.48\textwidth}
    % Empty subfigure to balance the grid
  \end{subfigure}

  \caption{Individual Estimated Factor Log-Volatilities ($\hat{\vh}_{t|T}$) (DFSV-BIF)}
  \label{fig:app_empirical_logvols_bif}
  \par\medskip \footnotesize Note: Smoothed estimates of the K=5 factor log-volatilities obtained from the DFSV-BIF model over the full sample period.
\end{figure}

\subsection{PF Log-Volatility Estimates}
\label{app:pf_logvols}

The following figures show the individual log-volatility estimates from the DFSV-PF model. These detailed plots provide a comparison with the BIF estimates and complement the overview presented in the main text.

\begin{figure}[htbp]
  \centering
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/logvol_1.png}
    \caption{Log-Volatility Factor 1}
    \label{fig:app_empirical_logvols_pf_h1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/logvol_2.png}
    \caption{Log-Volatility Factor 2}
    \label{fig:app_empirical_logvols_pf_h2}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/logvol_3.png}
    \caption{Log-Volatility Factor 3}
    \label{fig:app_empirical_logvols_pf_h3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/logvol_4.png}
    \caption{Log-Volatility Factor 4}
    \label{fig:app_empirical_logvols_pf_h4}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  \begin{subfigure}[b]{0.48\textwidth}
    \includegraphics[width=\textwidth]{empirical/insample/pf/figures/logvol_5.png}
    \caption{Log-Volatility Factor 5}
    \label{fig:app_empirical_logvols_pf_h5}
  \end{subfigure}
  \hfill % Add emptyhfill to balance the last row
  \begin{subfigure}[b]{0.48\textwidth}
    % Empty subfigure to balance the grid
  \end{subfigure}

  \caption{Individual Estimated Factor Log-Volatilities ($\hat{\vh}_{t|T}$) (DFSV-PF)}
  \label{fig:app_empirical_logvols_pf}
  \par\medskip \footnotesize Note: Smoothed estimates of the K=5 factor log-volatilities obtained from the DFSV-PF model over the full sample period.
\end{figure}

\section{Factor Structure Dynamics}
\label{app:factor_structure_dynamics}

This section presents the analysis of factor structure dynamics that complements the covariance dynamics analysis in the main text. It examines how the proportion of total variance attributed to common factors varies over time in the DFSV models compared to the constant-volatility DFM.

\begin{figure}[htbp]
  \centering
  \includegraphics[width=\textwidth]{empirical/insample/bif/figures/variance_decomposition.png}
  \caption{Factor Variance Contribution Ratio (FCR) Time Series. Time series of the Factor Variance Contribution Ratio ($FCR_t$), defined as the trace of the common component variance divided by the trace of the total conditional variance, for DFM, DFSV-BIF, and DFSV-PF models. It measures the proportion of total variance explained by common factors.}
  \label{fig:app_empirical_fcr}
\end{figure}

Figure~\ref{fig:app_empirical_fcr} displays the Factor Contribution Ratio (FCR) over time for the factor-based models. The FCR is defined as:
\begin{equation}
  \text{FCR}_t = \frac{\tr(\hat{\mLambda} \Var(\hat{\vf}_t) \hat{\mLambda}')}{\tr(\hat{\mSigma}_t)}.
\end{equation}

This ratio measures the proportion of total variance explained by common factors at each point in time. As expected, the DFM (Factor-CV) shows a constant FCR due to its static covariance assumption. In contrast, the DFSV-BIF model exhibits notable time variation in the proportion of variance attributed to common factors, with the ratio generally decreasing during periods of market stress. This suggests that idiosyncratic risks become relatively more important during crisis periods, a finding consistent with some previous studies on factor structures during market turbulence. The DFSV-PF model shows more erratic patterns in its FCR, consistent with its generally less stable state estimates observed in other metrics.

