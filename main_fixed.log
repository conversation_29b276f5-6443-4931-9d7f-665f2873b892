This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2025.3.2)  24 APR 2025 16:07
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**main_fixed.tex
(./main_fixed.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/base/report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaT
eX and XeLaTeX
\babel@savecnt=\count271
\U@D=\dimen142
\l@unhyphenated=\language36

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count272

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/babel-english/british.ldf
Language: british 2017/06/06 v3.3r English support from the babel system

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language9). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language9). Reported on input line 108.
)))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/babel/locale/en/babel-britis
h.tex
Package babel Info: Importing font and identification data for british
(babel)             from babel-en-GB.ini. Reported on input line 11.
) (/home/<USER>/texlive/2024/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count273
\Gm@cntv=\count274
\c@Gm@tempcnt=\count275
\Gm@bindingoffset=\dimen143
\Gm@wd@mp=\dimen144
\Gm@odd@mp=\dimen145
\Gm@even@mp=\dimen146
\Gm@layoutwidth=\dimen147
\Gm@layoutheight=\dimen148
\Gm@layouthoffset=\dimen149
\Gm@layoutvoffset=\dimen150
\Gm@dimlist=\toks18
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen151
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen152
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count276
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count277
\leftroot@=\count278
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count279
\DOTSCASE@=\count280
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen153
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count281
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count282
\dotsspace@=\muskip17
\c@parentequation=\count283
\dspbrk@lvl=\count284
\tag@help=\toks20
\row@=\count285
\column@=\count286
\maxfields@=\count287
\andhelp@=\toks21
\eqnshift@=\dimen154
\alignsep@=\dimen155
\tagshift@=\dimen156
\tagwidth@=\dimen157
\totwidth@=\dimen158
\lineht@=\dimen159
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.st
y
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/home/<USER>/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestrin
g.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/home/<USER>/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count288
) (/home/<USER>/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count289
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen160
\Hy@linkcounter=\count290
\Hy@pagecounter=\count291

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count292

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count293

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen161

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count294
\Field@Width=\dimen162
\Fld@charsize=\dimen163
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count295
\c@Item=\count296
\c@Hfootnote=\count297
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count298
\c@bookmark@seq@number=\count299

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.
sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.
sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip54
) (/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen166
\lightrulewidth=\dimen167
\cmidrulewidth=\dimen168
\belowrulesep=\dimen169
\belowbottomsep=\dimen170
\aboverulesep=\dimen171
\abovetopsep=\dimen172
\cmidrulesep=\dimen173
\cmidrulekern=\dimen174
\defaultaddspace=\dimen175
\@cmidla=\count300
\@cmidlb=\count301
\@aboverulesep=\dimen176
\@belowrulesep=\dimen177
\@thisruleclass=\count302
\@lastruleclass=\count303
\@thisrulewidth=\dimen178
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/apacite/apacite.sty
Package: apacite 2013/07/21 v6.03 APA citation
\c@BibCnt=\count304
\bibleftmargin=\skip55
\bibindent=\skip56
\bibparsep=\skip57
\bibitemsep=\skip58
\biblabelsep=\skip59
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
)
(./econometrics.sty)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count305
\float@exts=\toks24
\float@box=\box54
\@float@everytoks=\toks25
\@floatcapt=\box55
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count306
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count307
\c@ALG@rem=\count308
\c@ALG@nested=\count309
\ALG@tlm=\skip60
\ALG@thistlm=\skip61
\c@ALG@Lnr=\count310
\c@ALG@blocknr=\count311
\c@ALG@storecount=\count312
\c@ALG@tmpcounter=\count313
\ALG@tmplength=\skip62
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (/home/<USER>/texlive/2024/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen179
\ar@mcellbox=\box56
\extrarowheight=\dimen180
\NC@list=\toks27
\extratabsurround=\skip63
\backup@length=\skip64
\ar@cellbox=\box57
)
\TX@col@width=\dimen181
\TX@old@table=\dimen182
\TX@old@col=\dimen183
\TX@target=\dimen184
\TX@delta=\dimen185
\TX@cols=\count314
\TX@ftn=\toks28
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/mwe/mwe.sty
Package: mwe 2018/03/30 v0.5 Package to support minimal working examples (MWE)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/lipsum/lipsum.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.s
ty (/home/<USER>/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count315
\l__pdf_internal_box=\box58
))
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
Package: lipsum 2021-09-20 v2.7 150 paragraphs of Lorem Ipsum dummy text
\g__lipsum_par_int=\count316
\l__lipsum_a_int=\count317
\l__lipsum_b_int=\count318
 (/home/<USER>/texlive/2024/texmf-dist/tex/latex/lipsum/lipsum.ltd.tex))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/blindtext/blindtext.sty
Package: blindtext 2012/01/06 V2.0 blindtext-Package

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
)
\c@blindtext=\count319
\c@Blindtext=\count320
\c@blind@countparstart=\count321
\blind@countxx=\count322
\blindtext@numBlindtext=\count323
\blind@countyy=\count324
\c@blindlist=\count325
\c@blindlistlevel=\count326
\c@blindlist@level=\count327
\blind@listitem=\count328
\c@blind@listcount=\count329
\c@blind@levelcount=\count330
\blind@mathformula=\count331
\blind@Mathformula=\count332
\c@blind@randomcount=\count333
\c@blind@randommax=\count334
\c@blind@pangramcount=\count335
\c@blind@pangrammax=\count336
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
Package cleveref Info: `algorithmicx' support loaded on input line 3120.
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip65
\enit@outerparindent=\dimen186
\enit@toks=\toks29
\enit@inbox=\box59
\enit@count@id=\count337
\enitdp@description=\count338
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/nomencl/nomencl.sty
Package: nomencl 2021/11/10 v5.6 Nomenclature package

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks30
\XKV@tempa@toks=\toks31
)
\XKV@depth=\count339
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/koma-script/tocbasic.sty
Package: tocbasic 2024/10/24 v3.43 KOMA-Script package (handling toc-files)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2024/10/24 v3.43 KOMA-Script package (KOMA-Script-independent 
basics and keyval usage)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)


(/home/<USER>/texlive/2024/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))
Applying: [2021/05/01] Usage of raw or classic option list on input line 254.
Already applied: [0000/00/00] Usage of raw or classic option list on input line
 370.
)
\scr@dte@tocline@numberwidth=\skip66
\scr@dte@tocline@numbox=\box60
)
Package tocbasic Info: setting babel extension for `nlo' on input line 187.
Package tocbasic Info: setting babel extension for `nls' on input line 188.
\nomlabelwidth=\dimen187
\nom@tempdim=\dimen188
\nomitemsep=\skip67
)
\@nomenclaturefile=\write3
\openout3 = `main_fixed.nlo'.

Package nomencl Info: Writing nomenclature file main_fixed.nlo on input line 32
.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common
.tex
\pgfutil@everybye=\toks32
\pgfutil@tempdima=\dimen189
\pgfutil@tempdimb=\dimen190
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.
def
\pgfutil@abb=\box61
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.te
x (/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.
tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.t
ex
\pgfkeys@pathtoks=\toks33
\pgfkeys@temptoks=\toks34

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibrary
filtered.code.tex
\pgfkeys@tmptoks=\toks35
))
\pgf@x=\dimen191
\pgf@y=\dimen192
\pgf@xa=\dimen193
\pgf@ya=\dimen194
\pgf@xb=\dimen195
\pgf@yb=\dimen196
\pgf@xc=\dimen197
\pgf@yc=\dimen198
\pgf@xd=\dimen199
\pgf@yd=\dimen256
\w@pgf@writea=\write4
\r@pgf@reada=\read3
\c@pgf@counta=\count340
\c@pgf@countb=\count341
\c@pgf@countc=\count342
\c@pgf@countd=\count343
\t@pgf@toka=\toks36
\t@pgf@tokb=\toks37
\t@pgf@tokc=\toks38
\pgf@sys@id@count=\count344

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdfte
x.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-commo
n-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpa
th.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count345
\pgfsyssoftpath@bigbuffer@items=\count346
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotoc
ol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.
tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.te
x)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.
tex
\pgfmath@dimen=\dimen257
\pgfmath@count=\count347
\pgfmath@box=\box62
\pgfmath@toks=\toks39
\pgfmath@stack@operand=\toks40
\pgfmath@stack@operation=\toks41
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.co
de.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.ba
sic.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.tr
igonometric.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.ra
ndom.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.co
mparison.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.ba
se.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.ro
und.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.mi
sc.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.in
tegerarithmetics.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.te
x)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.t
ex
\c@pgfmathroundto@lastzeros=\count348
))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints
.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen258
\pgf@picmaxx=\dimen259
\pgf@picminy=\dimen260
\pgf@picmaxy=\dimen261
\pgf@pathminx=\dimen262
\pgf@pathmaxx=\dimen263
\pgf@pathminy=\dimen264
\pgf@pathmaxy=\dimen265
\pgf@xx=\dimen266
\pgf@xy=\dimen267
\pgf@yx=\dimen268
\pgf@yy=\dimen269
\pgf@zx=\dimen270
\pgf@zy=\dimen271
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathco
nstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen272
\pgf@path@lasty=\dimen273
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathus
age.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen274
\pgf@shorten@start@additional=\dimen275
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes
.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count349
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphi
cstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen276
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransf
ormations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen277
\pgf@pt@y=\dimen278
\pgf@pt@temp=\dimen279
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.
code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobject
s.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathpr
ocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows
.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen280
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.
code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen281
\pgf@sys@shading@range@num=\count350
\pgf@shadingcount=\count351
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.
code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreextern
al.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers
.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransp
arency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatter
ns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.co
de.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.
code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box67
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.co
de.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-vers
ion-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen282
\pgf@nodesepend=\dimen283
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-vers
ion-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.t
ex)) (/home/<USER>/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.te
x
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen284
\pgffor@skip=\dimen285
\pgffor@stack=\toks42
\pgffor@toks=\toks43
))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.
code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplot
handlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count352
\pgfplotmarksize=\dimen286
)
\tikz@lastx=\dimen287
\tikz@lasty=\dimen288
\tikz@lastxsaved=\dimen289
\tikz@lastysaved=\dimen290
\tikz@lastmovetox=\dimen291
\tikz@lastmovetoy=\dimen292
\tikzleveldistance=\dimen293
\tikzsiblingdistance=\dimen294
\tikz@figbox=\box68
\tikz@figbox@bg=\box69
\tikz@tempbox=\box70
\tikz@tempbox@bg=\box71
\tikztreelevel=\count353
\tikznumberofchildren=\count354
\tikznumberofcurrentchild=\count355
\tikz@fig@count=\count356

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.
code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count357
\pgfmatrixcurrentcolumn=\count358
\pgf@matrix@numberofcolumns=\count359
)
\tikz@expandcount=\count360

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibr
aryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarro
ws.meta.code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen295
\pgfarrowlength=\dimen296
\pgfarrowwidth=\dimen297
\pgfarrowlinewidth=\dimen298
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibraryfit.code.tex
File: tikzlibraryfit.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/home/<USER>/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libra
ries/tikzlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@layerbox@background=\box72
\pgf@layerboxsaved@background=\box73
) (./main_fixed.aux (./introduction.aux)
(./methodology.aux) (./data.aux) (./simulation.aux) (./empirical.aux)
(./conclusion.aux) (./appendix.aux))
\openout1 = `main_fixed.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 112.
(./main_fixed.out) (./main_fixed.out)
\@outlinefile=\write5
\openout5 = `main_fixed.out'.


(/home/<USER>/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count361
\scratchdimen=\dimen299
\scratchbox=\box74
\nofMPsegments=\count362
\nofMParguments=\count363
\everyMPshowfont=\toks44
\MPscratchCnt=\count364
\MPscratchDim=\dimen300
\MPnumerator=\count365
\makeMPintoPDFobject=\count366
\everyMPtoPDFconversion=\toks45
)
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
\c@maskedRefs=\count367
(/home/<USER>/texlive/2024/texmf-dist/tex/latex/apacite/english.apc
File: english.apc 2013/07/21 v6.03 apacite language file
LaTeX Info: Redefining \BPBI on input line 129.
LaTeX Info: Redefining \BHBI on input line 130.
)
(./nomenclature.tex)
<eur.png, id=175, 102.71375pt x 34.53903pt>
File: eur.png Graphic file (type png)
<use eur.png>
Package pdftex.def Info: eur.png  used on input line 133.
(pdftex.def)             Requested size: 313.55228pt x 105.43657pt.
LaTeX Font Info:    Trying to load font information for U+msa on input line 136
.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 136
.

(/home/<USER>/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{/home/<USER>/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map} <./eu
r.png>]


pdfTeX warning (ext4): destination with the same identifier (name{page.i}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.152 \end{abstract}
                     [1] (./main_fixed.toc


pdfTeX warning (ext4): destination with the same identifier (name{page.i}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.121 ...\numberline {6}Conclusion}{43}{chapter.6}
                                                  % [1

])
\tf@toc=\write6
\openout6 = `main_fixed.toc'.



[2] (./main_fixed.nls

[3


])

[4]
\openout2 = `introduction.aux'.

 (./introduction.tex
Chapter 1.

LaTeX Warning: Citation `cont_empirical_2001' undefined on input line 9.


LaTeX Warning: Citation `han_asset_2006' undefined on input line 9.


LaTeX Warning: Citation `borghi_dynamics_2018' undefined on input line 9.


LaTeX Warning: Citation `aguilar_bayesian_2000' undefined on input line 14.


LaTeX Warning: Citation `philipov_factor_2006' undefined on input line 14.



[1



]

LaTeX Warning: Citation `bollerslev_chapter_1994' undefined on input line 19.


LaTeX Warning: Citation `engle_dynamic_2000' undefined on input line 19.


LaTeX Warning: Citation `shumway_approach_1982' undefined on input line 21.


LaTeX Warning: Citation `lange_bellman_2024' undefined on input line 21.


LaTeX Warning: Citation `kim_stochastic_1998' undefined on input line 23.


LaTeX Warning: Citation `kastner_efficient_2017' undefined on input line 23.


LaTeX Warning: Citation `malik_modelling_2011' undefined on input line 23.


LaTeX Warning: Citation `rebeschini_can_2015' undefined on input line 23.



[2]

[3{/home/<USER>/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.
enc}])

[4]
\openout2 = `methodology.aux'.

 (./methodology.tex
Chapter 2.


[5




]
Overfull \hbox (58.7404pt too wide) in paragraph at lines 45--46
 []\OT1/cmr/bx/n/10.95 Log-Volatility Dy-nam-ics:[] \OT1/cmr/m/n/10.95 The lat-
ent log-volatilities of the factors, de-noted by $\OML/cmm/b/it/10.95 h[] \OT1/
cmr/m/n/10.95 = (\OML/cmm/m/it/10.95 h[]; h[]; [] ; h[]\OT1/cmr/m/n/10.95 )[]$,

 []



[6]

[7]

LaTeX Warning: Reference `app:bf_loglik' on page 8 undefined on input line 110.




[8]
Overfull \hbox (1.93848pt too wide) in paragraph at lines 169--170
 []\OT1/cmr/bx/n/10.95 Innovation Co-v-ari-ance:[] \OT1/cmr/m/n/10.95 The co-v-
ari-ance mat-rix of state in-nov-a-tions, $[]$, is block-diagonal,
 []



[9]

LaTeX Warning: Reference `app:matrix_identities' on page 10 undefined on input 
line 185.


Overfull \hbox (0.39603pt too wide) in paragraph at lines 185--186
 []\OT1/cmr/bx/n/10.95 Information Pre-dic-tion (Joseph Form):[] \OT1/cmr/m/n/1
0.95 The BIF propag-ates the pre-ci-sion mat-rix $\OT1/cmr/bx/n/10.95 
[] \OT1/cmr/m/n/10.95 =
 []

Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on in
put line 209.

Overfull \hbox (31.39372pt too wide) in paragraph at lines 219--220
[] \OT1/cmr/bx/n/10.95 Up-date $\OML/cmm/b/it/10.95 f[] \OT1/cmr/m/n/10.95 = []
[] []$ 
 []



[10]

LaTeX Warning: Reference `app:factor_update' on page 11 undefined on input line
 232.


LaTeX Warning: Reference `app:logvol_update' on page 11 undefined on input line
 239.


LaTeX Warning: Reference `app:fim' on page 11 undefined on input line 257.



[11]

LaTeX Warning: Reference `app:bcd_efficiency' on page 12 undefined on input lin
e 272.


pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.1}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.289 \State
             \textbf{Initialize} particles $\{\valpha_0^{(i)}\}_{i=1}^P$ and...

pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.2}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.291 \For
          {$t = 1$ to $T$}
pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.3}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.292   \State
               \textbf{Predict:} Propagate particles through state transitio...

pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.4}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.298   \State
               \textbf{Update:} Compute importance weights using the observa...

pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.5}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.303   \State
               \textbf{Normalize} weights: $w_t^{(i)} = \tilde{w}_t^{(i)} / ...

pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.6}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.305   \State
               \textbf{Calculate} Effective Sample Size (ESS): $\text{ESS}_t...

pdfTeX warning (ext4): destination with the same identifier (name{ALG@line.7}) 
has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.307   \If
           {$\text{ESS}_t < \text{ESS}_{\text{threshold}}$}

[12]
Overfull \hbox (9.88594pt too wide) in paragraph at lines 330--331
 []\OT1/cmr/bx/n/10.95 Likelihood Cal-cu-la-tion for Para-meter Op-tim-iz-a-tio
n:[] \OT1/cmr/m/n/10.95 Bey-ond state es-tim-a-tion, the Particle
 []



[13]

[14]

[15]

LaTeX Warning: Reference `app:matrix_identities' on page 16 undefined on input 
line 411.

)

[16]

[17]
\openout2 = `data.aux'.

 (./data.tex
Chapter 3.

LaTeX Warning: Citation `fama_common_1993' undefined on input line 12.



[18




]
Overfull \hbox (168.62657pt too wide) in paragraph at lines 28--41
 [][] 
 []

<eda/figure1_returns_timeseries.png, id=524, 867.24pt x 867.24pt>
File: eda/figure1_returns_timeseries.png Graphic file (type png)
<use eda/figure1_returns_timeseries.png>
Package pdftex.def Info: eda/figure1_returns_timeseries.png  used on input line
 49.
(pdftex.def)             Requested size: 455.24411pt x 455.24167pt.
<eda/figure2_avg_correlation_heatmap.png, id=527, 813.0375pt x 698.1282pt>
File: eda/figure2_avg_correlation_heatmap.png Graphic file (type png)
<use eda/figure2_avg_correlation_heatmap.png>
Package pdftex.def Info: eda/figure2_avg_correlation_heatmap.png  used on input
 line 58.
(pdftex.def)             Requested size: 364.19667pt x 312.71669pt.


[19
pdfTeX warning (ext4): destination with the same identifier (name{table.3.1}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.83 \end{table}
                ]

[20
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.1}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.83 \end{table}
                 <./eda/figure1_returns_timeseries.png>]
<eda/figure3_avg_characteristics_timeseries.png, id=545, 999.4941pt x 493.845pt
>
File: eda/figure3_avg_characteristics_timeseries.png Graphic file (type png)
<use eda/figure3_avg_characteristics_timeseries.png>
Package pdftex.def Info: eda/figure3_avg_characteristics_timeseries.png  used o
n input line 91.
(pdftex.def)             Requested size: 455.24411pt x 224.93341pt.
<eda/figure4_avg_mkt_cap_by_size.png, id=547, 855.195pt x 421.8159pt>
File: eda/figure4_avg_mkt_cap_by_size.png Graphic file (type png)
<use eda/figure4_avg_mkt_cap_by_size.png>
Package pdftex.def Info: eda/figure4_avg_mkt_cap_by_size.png  used on input lin
e 100.
(pdftex.def)             Requested size: 318.66948pt x 157.1765pt.


[21
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.2}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.104 
      
pdfTeX warning (ext4): destination with the same identifier (name{table.3.2}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.104 
       <./eda/figure2_avg_correlation_heatmap.png>]

[22
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.3}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.104 
      
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.4}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.104 
       <./eda/figure3_avg_characteristics_timeseries.png> <./eda/figure4_avg_mk
t_cap_by_size.png>]
<eda/figure5_pca_scree_plot.png, id=565, 999.2532pt x 566.3559pt>
File: eda/figure5_pca_scree_plot.png Graphic file (type png)
<use eda/figure5_pca_scree_plot.png>
Package pdftex.def Info: eda/figure5_pca_scree_plot.png  used on input line 113
.
(pdftex.def)             Requested size: 318.66948pt x 180.60672pt.


LaTeX Warning: Citation `fama_five-factor_2015' undefined on input line 121.



[23
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.5}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.125 
       <./eda/figure5_pca_scree_plot.png>]

LaTeX Warning: Reference `sec:dfsv_model' on page 24 undefined on input line 13
1.


LaTeX Warning: Citation `engle_dynamic_2002' undefined on input line 134.



[24]
Overfull \hbox (8.6005pt too wide) in paragraph at lines 154--155
[]\OT1/cmr/bx/n/10.95 Information Cri-teria (AIC/BIC): \OT1/cmr/m/n/10.95 The A
kaike In-form-a-tion Cri-terion (AIC) and Bayesian
 []



[25]

[26]

[27]

[28
pdfTeX warning (ext4): destination with the same identifier (name{figure.3.6}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.290 
      ]

[29
pdfTeX warning (ext4): destination with the same identifier (name{table.3.3}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.318 
      ]
Underfull \hbox (badness 10000) in paragraph at lines 342--342
[]|\OT1/cmr/m/n/9 Fixed val-ues, e.g.,
 []


Underfull \hbox (badness 3058) in paragraph at lines 342--342
\OT1/cmr/m/n/9 [\OMS/cmsy/m/n/9 ^^@\OT1/cmr/m/n/9 1\OML/cmm/m/it/9 :\OT1/cmr/m/
n/9 0\OML/cmm/m/it/9 ; \OMS/cmsy/m/n/9 ^^@\OT1/cmr/m/n/9 0\OML/cmm/m/it/9 :\OT1
/cmr/m/n/9 5]$ for $\OML/cmm/m/it/9 K \OT1/cmr/m/n/9 = 2$
 []


Underfull \hbox (badness 10000) in paragraph at lines 342--342
\OT1/cmr/m/n/9 or $[\OMS/cmsy/m/n/9 ^^@\OT1/cmr/m/n/9 1\OML/cmm/m/it/9 :\OT1/cm
r/m/n/9 0\OML/cmm/m/it/9 ; [] ; \OMS/cmsy/m/n/9 ^^@\OT1/cmr/m/n/9 1\OML/cmm/m/i
t/9 :\OT1/cmr/m/n/9 0]$ for
 []



[30
pdfTeX warning (ext4): destination with the same identifier (name{table.3.4}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.352 T
       he PF approach maximizes the standard likelihood (Section~\ref{sec:pa...
]

LaTeX Warning: Reference `app:optimizer_comparison' on page 31 undefined on inp
ut line 354.


Overfull \hbox (1.92027pt too wide) in paragraph at lines 372--373
 []\OT1/cmr/bx/n/10.95 2. State Es-tim-a-tion Ac-cur-acy:[] \OT1/cmr/m/n/10.95 
We eval-u-ate the qual-ity of the filtered lat-ent states ($[]\OML/cmm/m/it/10.
95 ; []$\OT1/cmr/m/n/10.95 )
 []



[31])

[32]
\openout2 = `simulation.aux'.

 (./simulation.tex
Chapter 4.

Overfull \hbox (7.96355pt too wide) in paragraph at lines 5--5
[]\OT1/cmr/bx/n/14.4 Simulation Study 1: Com-pu-ta-tional Per-form-ance and Sca
lab-
 []


LaTeX Warning: Reference `tab:dgp_parameters' on page 33 undefined on input lin
e 11.


LaTeX Warning: Reference `ch:data_simulation' on page 33 undefined on input lin
e 11.



[33




]

LaTeX Warning: Reference `app:sim1_supplementary' on page 34 undefined on input
 line 36.

<simulation1_analysis/figures/median_time_vs_K_N50_detail.png, id=682, 867.24pt
 x 505.89pt>
File: simulation1_analysis/figures/median_time_vs_K_N50_detail.png Graphic file
 (type png)
<use simulation1_analysis/figures/median_time_vs_K_N50_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_time_vs_K_N50_deta
il.png  used on input line 48.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.
<simulation1_analysis/figures/median_time_vs_N_K5_detail.png, id=683, 867.24pt 
x 505.89pt>
File: simulation1_analysis/figures/median_time_vs_N_K5_detail.png Graphic file 
(type png)
<use simulation1_analysis/figures/median_time_vs_N_K5_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_time_vs_N_K5_detai
l.png  used on input line 54.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.


[34
pdfTeX warning (ext4): destination with the same identifier (name{table.4.1}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.61 
     
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.1}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.61 
     
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.2}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.61 
      <./simulation1_analysis/figures/median_time_vs_K_N50_detail.png> <./simul
ation1_analysis/figures/median_time_vs_N_K5_detail.png>]
<simulation1_analysis/figures/heatmap_time_bif.png, id=702, 722.7pt x 578.16pt>

File: simulation1_analysis/figures/heatmap_time_bif.png Graphic file (type png)

<use simulation1_analysis/figures/heatmap_time_bif.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_time_bif.png  use
d on input line 73.
(pdftex.def)             Requested size: 218.51521pt x 174.80792pt.
<simulation1_analysis/figures/heatmap_rmse_h_bif.png, id=703, 722.7pt x 578.16p
t>
File: simulation1_analysis/figures/heatmap_rmse_h_bif.png Graphic file (type pn
g)
<use simulation1_analysis/figures/heatmap_rmse_h_bif.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_rmse_h_bif.png  u
sed on input line 79.
(pdftex.def)             Requested size: 218.51521pt x 174.80792pt.
<simulation1_analysis/figures/median_rmse_f_vs_K_N50_detail.png, id=706, 867.24
pt x 505.89pt>
File: simulation1_analysis/figures/median_rmse_f_vs_K_N50_detail.png Graphic fi
le (type png)
<use simulation1_analysis/figures/median_rmse_f_vs_K_N50_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_rmse_f_vs_K_N50_de
tail.png  used on input line 99.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.
<simulation1_analysis/figures/median_corr_f_vs_K_N50_detail.png, id=707, 867.24
pt x 505.89pt>
File: simulation1_analysis/figures/median_corr_f_vs_K_N50_detail.png Graphic fi
le (type png)
<use simulation1_analysis/figures/median_corr_f_vs_K_N50_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_corr_f_vs_K_N50_de
tail.png  used on input line 105.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.


[35
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.3}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.109 \end{figure}
                  
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.4}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.109 \end{figure}
                   <./simulation1_analysis/figures/heatmap_time_bif.png> <./sim
ulation1_analysis/figures/heatmap_rmse_h_bif.png>]
<simulation1_analysis/figures/median_rmse_h_vs_K_N50_detail.png, id=722, 867.24
pt x 505.89pt>
File: simulation1_analysis/figures/median_rmse_h_vs_K_N50_detail.png Graphic fi
le (type png)
<use simulation1_analysis/figures/median_rmse_h_vs_K_N50_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_rmse_h_vs_K_N50_de
tail.png  used on input line 121.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.
<simulation1_analysis/figures/median_corr_h_vs_K_N50_detail.png, id=723, 867.24
pt x 505.89pt>
File: simulation1_analysis/figures/median_corr_h_vs_K_N50_detail.png Graphic fi
le (type png)
<use simulation1_analysis/figures/median_corr_h_vs_K_N50_detail.png>
Package pdftex.def Info: simulation1_analysis/figures/median_corr_h_vs_K_N50_de
tail.png  used on input line 127.
(pdftex.def)             Requested size: 218.51521pt x 127.46025pt.


[36
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.5}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.135 T
       hese findings highlight a key strength of the BIF: its superior abili...

pdfTeX warning (ext4): destination with the same identifier (name{figure.4.6}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.135 T
       hese findings highlight a key strength of the BIF: its superior abili...

pdfTeX warning (ext4): destination with the same identifier (name{figure.4.7}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.135 T
       hese findings highlight a key strength of the BIF: its superior abili...

pdfTeX warning (ext4): destination with the same identifier (name{figure.4.8}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.135 T
       hese findings highlight a key strength of the BIF: its superior abili...
 <./simulation1_analysis/figures/median_rmse_f_vs_K_N50_detail.png> <./simulati
on1_analysis/figures/median_corr_f_vs_K_N50_detail.png> <./simulation1_analysis
/figures/median_rmse_h_vs_K_N50_detail.png> <./simulation1_analysis/figures/med
ian_corr_h_vs_K_N50_detail.png>]

LaTeX Warning: Citation `Gordon1993' undefined on input line 141.


LaTeX Warning: Citation `Doucet2001' undefined on input line 141.


LaTeX Warning: Reference `app:sim1_supplementary' on page 37 undefined on input
 line 141.


LaTeX Warning: Reference `app:computational_notes' on page 37 undefined on inpu
t line 143.



[37]
<example-image-a.pdf, id=765, 321.2pt x 240.9pt>
File: example-image-a.pdf Graphic file (type pdf)
<use example-image-a.pdf>
Package pdftex.def Info: example-image-a.pdf  used on input line 228.
(pdftex.def)             Requested size: 218.51521pt x 163.89069pt.
<example-image-b.pdf, id=766, 321.2pt x 240.9pt>
File: example-image-b.pdf Graphic file (type pdf)
<use example-image-b.pdf>
Package pdftex.def Info: example-image-b.pdf  used on input line 230.
(pdftex.def)             Requested size: 218.51521pt x 163.89069pt.
<example-image-c.pdf, id=767, 321.2pt x 240.9pt>
File: example-image-c.pdf Graphic file (type pdf)
<use example-image-c.pdf>
Package pdftex.def Info: example-image-c.pdf  used on input line 249.
(pdftex.def)             Requested size: 455.24411pt x 341.44446pt.


LaTeX Warning: Reference `app:sim2_bias_appendix' on page 38 undefined on input
 line 257.



[38
pdfTeX warning (ext4): destination with the same identifier (name{table.4.2}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.276 \subsection
                 {Dimensional scaling}\label{sec:sim2:scaling}
pdfTeX warning (ext4): destination with the same identifier (name{table.4.3}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.276 \subsection
                 {Dimensional scaling}\label{sec:sim2:scaling}]

[39
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.9}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.276 \subsection
                 {Dimensional scaling}\label{sec:sim2:scaling}
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.10})
 has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.276 \subsection
                 {Dimensional scaling}\label{sec:sim2:scaling}
pdfTeX warning (ext4): destination with the same identifier (name{table.4.4}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.276 \subsection
                 {Dimensional scaling}\label{sec:sim2:scaling} </home/<USER>/t
exlive/2024/texmf-dist/tex/latex/mwe/example-image-a.pdf> </home/<USER>/texliv
e/2024/texmf-dist/tex/latex/mwe/example-image-b.pdf> </home/<USER>/texlive/202
4/texmf-dist/tex/latex/mwe/example-image-c.pdf>]
<example-image-16x9.pdf, id=809, 321.2pt x 180.675pt>
File: example-image-16x9.pdf Graphic file (type pdf)
<use example-image-16x9.pdf>
Package pdftex.def Info: example-image-16x9.pdf  used on input line 282.
(pdftex.def)             Requested size: 455.24411pt x 256.08333pt.
)

[40
pdfTeX warning (ext4): destination with the same identifier (name{figure.4.11})
 has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.165 \include{simulation}
                          
pdfTeX warning (ext4): destination with the same identifier (name{table.4.5}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.165 \include{simulation}
                           </home/<USER>/texlive/2024/texmf-dist/tex/latex/mwe
/example-image-16x9.pdf>]
\openout2 = `empirical.aux'.

 (./empirical.tex
Chapter 5.


[41




]

LaTeX Warning: Reference `sec:simulation:computational_efficiency' on page 42 u
ndefined on input line 116.

)

[42]
\openout2 = `conclusion.aux'.

 (./conclusion.tex
Chapter 6.
)

[43




]
No file main_fixed.bbl.
\openout2 = `appendix.aux'.

(./appendix.tex
Appendix A.


[44




]

LaTeX Warning: Reference `app:fim' on page 45 undefined on input line 67.



[45]

[46]

[47]

[48]

LaTeX Warning: Reference `eq:marg_loglik_expanded' on page 49 undefined on inpu
t line 214.


LaTeX Warning: Reference `app:matrix_identities' on page 49 undefined on input 
line 216.


LaTeX Warning: Reference `eq:det_lemma' on page 49 undefined on input line 216.



LaTeX Warning: Reference `eq:woodbury' on page 49 undefined on input line 216.



[49]

[50]

[51]

[52]

[53]

[54]

[55]

[56]

[57]

LaTeX Warning: Reference `ch:data_simulation' on page 58 undefined on input lin
e 563.



[58]
Appendix B.

LaTeX Warning: Reference `tab:bif_stability_summary' on page 59 undefined on in
put line 573.


LaTeX Warning: Reference `tab:pf_stability_summary' on page 59 undefined on inp
ut line 573.

(./simulation1_analysis/tables/bif_stability_summary.tex
Overfull \hbox (89.82831pt too wide) in paragraph at lines 5--21
[][] 
 []

) (./simulation1_analysis/tables/pf_stability_summary.tex
Overfull \hbox (144.2596pt too wide) in paragraph at lines 5--21
[][] 
 []

)

[59


pdfTeX warning (ext4): destination with the same identifier (name{table.B.1}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.588 
      ]
<simulation1_analysis/figures/heatmap_corr_f_bif.png, id=1059, 722.7pt x 578.16
pt>
File: simulation1_analysis/figures/heatmap_corr_f_bif.png Graphic file (type pn
g)
<use simulation1_analysis/figures/heatmap_corr_f_bif.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_corr_f_bif.png  u
sed on input line 592.
(pdftex.def)             Requested size: 318.66948pt x 254.93814pt.
<simulation1_analysis/figures/heatmap_corr_f_pf20000.png, id=1060, 722.7pt x 57
8.16pt>
File: simulation1_analysis/figures/heatmap_corr_f_pf20000.png Graphic file (typ
e png)
<use simulation1_analysis/figures/heatmap_corr_f_pf20000.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_corr_f_pf20000.pn
g  used on input line 599.
(pdftex.def)             Requested size: 318.66948pt x 254.93814pt.


LaTeX Warning: Reference `fig:app:heatmap_corr_f_bif' on page 60 undefined on i
nput line 600.


LaTeX Warning: Reference `fig:app:heatmap_corr_f_bif' on page 60 undefined on i
nput line 600.

<simulation1_analysis/figures/heatmap_rmse_h_pf20000.png, id=1061, 722.7pt x 57
8.16pt>
File: simulation1_analysis/figures/heatmap_rmse_h_pf20000.png Graphic file (typ
e png)
<use simulation1_analysis/figures/heatmap_rmse_h_pf20000.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_rmse_h_pf20000.pn
g  used on input line 606.
(pdftex.def)             Requested size: 318.66948pt x 254.93814pt.
<simulation1_analysis/figures/heatmap_time_pf20000.png, id=1064, 722.7pt x 578.
16pt>
File: simulation1_analysis/figures/heatmap_time_pf20000.png Graphic file (type 
png)
<use simulation1_analysis/figures/heatmap_time_pf20000.png>
Package pdftex.def Info: simulation1_analysis/figures/heatmap_time_pf20000.png 
 used on input line 613.
(pdftex.def)             Requested size: 318.66948pt x 254.93814pt.


[60
pdfTeX warning (ext4): destination with the same identifier (name{table.B.2}) h
as been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.622 \paragraph
                {Stability and Trade-off Analysis:}
pdfTeX warning (ext4): destination with the same identifier (name{figure.B.1}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.622 \paragraph
                {Stability and Trade-off Analysis:} <./simulation1_analysis/fig
ures/heatmap_corr_f_bif.png>]

[61
pdfTeX warning (ext4): destination with the same identifier (name{figure.B.2}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.622 \paragraph
                {Stability and Trade-off Analysis:}
pdfTeX warning (ext4): destination with the same identifier (name{figure.B.3}) 
has been already used, duplicate ignored
<argument> ...shipout:D \box_use:N \l_shipout_box 
                                                  \__shipout_drop_firstpage_...
l.622 \paragraph
                {Stability and Trade-off Analysis:} <./simulation1_analysis/fig
ures/heatmap_corr_f_pf20000.png> <./simulation1_analysis/figures/heatmap_rmse_h
_pf20000.png>]
<simulation1_analysis/figures/stability_ratio_f_vs_K_N50_log.png, id=1085, 722.
7pt x 433.62pt>
File: simulation1_analysis/figures/stability_ratio_f_vs_K_N50_log.png Graphic f
ile (type png)
<use simulation1_analysis/figures/stability_ratio_f_vs_K_N50_log.png>
Package pdftex.def Info: simulation1_analysis/figures/stability_ratio_f_vs_K_N5
0_log.png  used on input line 628.
(pdftex.def)             Requested size: 318.66948pt x 191.20361pt.
<simulation1_analysis/figures/tradeoff_rmse_h_vs_time.png, id=1086, 794.97pt x 
505.89pt>
File: simulation1_analysis/figures/tradeoff_rmse_h_vs_time.png Graphic file (ty
pe png)
<use simulation1_analysis/figures/tradeoff_rmse_h_vs_time.png>
Package pdftex.def Info: simulation1_analysis/figures/tradeoff_rmse_h_vs_time.p
ng  used on input line 635.
(pdftex.def)             Requested size: 318.66