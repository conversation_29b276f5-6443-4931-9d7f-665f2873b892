# LaTeX Build Files
## Core latex/pdflatex auxiliary files:
*.aux
*.lof
*.log
*.lot
*.fls
*.out
*.toc
*.fmt
*.fot
*.cb
*.cb2
.*.lb

## Intermediate documents:
*.dvi
*.xdv
*-converted-to.*

## Generated if empty string is given at "Please type another file name for output:"
.pdf

## Bibliography auxiliary files (bibtex/biblatex/biber):
*.bbl
*.bbl-SAVE-ERROR
*.bcf
*.bcf-SAVE-ERROR
*.blg
*-blx.aux
*-blx.bib
*.run.xml

## Build tool auxiliary files:
*.fdb_latexmk
*.synctex
*.synctex(busy)
*.synctex.gz
*.synctex.gz(busy)
*.pdfsync
*.rubbercache
rubber.cache

## Build tool directories for auxiliary files
# latexrun
latex.out/

## Xindy:
*.xdy

## xypic precompiled matrices:
*.xyc

## endfloat:
*.ttt
*.fff

## achemso:
acs-*.bib

## amsthm:
*.thm

## beamer:
*.nav
*.pre
*.snm
*.vrb
*.fdb_latexmk
*.make
*.figlist
*.makefile

## Changes:
*.soc

## Comment:
*.cut

## Cprotect:
*.cpt

## Enumitem:
*.enu

## Exam:
*.exa

## Fixme:
*.lox

## Fontspec:
*.fls

## Glossaries:
*.acn
*.acr
*.glg
*.glo
*.gls
*.glsdefs
*.lzo
*.lzs

## Gnuplottex:
*-gnuplottex-*

## Gregoriotex:
*.gaux
*.gtex

## Htlatex:
*.4ct
*.4tc
*.idv
*.lg
*.trc
*.xref

## Hyperref:
*.brf

## Knitr:
*-concordance.tex

## Latexindent backup files:
*.bak[0-9]*
*.orig

## Listings:
*.lol

## Luatex:
*.luac

## Makeindex:
*.ilg
*.ind
*.idx
*.ist

## Minitoc:
*.maf
*.mlf
*.mlt
*.mtc[0-9]*
*.slf[0-9]*
*.slt[0-9]*
*.stc[0-9]*

## Minted:
_minted*
*.pyg

## Morewrites:
*.mw

## Nomencl:
*.nlg
*.nlo
*.nls

## Pax:
*.pax

## Pdfpcnotes:
*.pdfpc

## Sagetex:
*.sagetex.sage
*.sagetex.py
*.sagetex.scmd

## Scrwfile:
*.wrt

## Sympy:
*.sout
*.sympy
sympy-plots-for-*.tex/

## Pdfcomment:
*.upa
*.upb

## Pythontex:
*.pytxcode
pythontex-files-*/

## Tcolorbox:
*.figlist
*.makefile

## Thmtools:
*.loe

## TikZ & PGF:
*.figlist
*.makefile
*.fls
*.fdb_latexmk

## Todonotes:
*.tdo

## Vhistory:
*.hst
*.ver

## Easy-todo:
*.lod

## Xcolor:
*.xcp

## Xmpincl:
*.xmpi

## Xindy:
*.xdy

## Xypic precompiled matrices and PDF files:
*.xyc
*.xyd

# Editor and System Files
## VS Code:
.vscode/
*.code-workspace

## macOS:
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

## Windows:
*.tmp
~$*

## Linux:
*~

# Python Files (for any Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
*.egg-info/
.pytest_cache/

# Keep the final PDF but ignore intermediate builds
# Uncomment the line below if you want to ignore the main PDF output too
# main.pdf

# Keep important files
!README.md
!references.bib
!outline.md
!submission_checklist.md
!build.sh
