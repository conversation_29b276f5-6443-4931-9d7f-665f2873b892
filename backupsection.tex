\section{Estimation Method Comparison: DFSV-BIF vs. DFSV-PF}
\label{sec:empirical:bif_vs_pf}

% Objective: Provide a focused synthesis comparing the two advanced methods used to estimate the same DFSV model specification.

\subsection{Statistical Fit Objective}
\label{sec:empirical:fit_objective}
We now turn to a direct comparison of the two advanced estimation methods employed for the DFSV model: the Bellman Information Filter (BIF) and the Particle Filter (PF). A first point of comparison is the overall statistical fit, typically assessed using log-likelihood values and information criteria like AIC and BIC, presented in Table~\ref{tab:empirical_overall_fit}.
% Revisit Table 5.3 (LL/AIC/BIC).
\begin{table}[htbp]
  \centering
  \caption{Overall Model Fit Comparison (Full Sample)}
  \label{tab:empirical_overall_fit}
  \begin{tabular}{lcccc}
    \toprule
    Model       & (Pseudo) Log-Likelihood & Num Params ($p$) & AIC      & BIC      \\
    \midrule
    DFSV-BIF    & 222,084.10              & 610              & -442,948.21 & -440,139.80 \\
    DFSV-PF     & -17,969.62              & 610              & 37,159.23   & 39,967.64   \\
    DFM         & -38,862.75              & 580              & 78,885.50   & 81,555.79   \\
    DCC-GARCH   & 162,516.36              & 288              & -324,456.72 & -323,130.79 \\
    \bottomrule
  \end{tabular}
  \par\medskip \footnotesize Note: Comparison of overall statistical fit based on the full sample (T=738). Log-likelihood for DFSV-BIF and DFSV-PF is the pseudo-log-likelihood evaluated at the estimated parameters. AIC and BIC penalize for the number of parameters ($p$). Lower AIC/BIC indicates a preferred model, but caution is advised when comparing true LL (DFM, DCC) with pseudo-LL (BIF, PF).
\end{table}



The table reveals a vast difference in the reported objective function values. The DFSV-BIF achieves a pseudo-log-likelihood of 222,084.10, while the DFSV-PF yields an approximate marginal log-likelihood of -17,969.62. It is crucial to reiterate that these values represent different quantities: the BIF optimizes a pseudo-likelihood based on Gaussian approximations, whereas the PF targets an approximation of the true marginal likelihood via simulation. Consequently, these likelihood values, along with the derived AIC and BIC, cannot be directly compared to select between the BIF and PF methods themselves. However, the sheer magnitude of the difference is noteworthy. It might suggest that the pseudo-likelihood surface optimized by the BIF is substantially different from the approximate marginal likelihood landscape explored by the PF, potentially hinting at challenges faced by the PF optimization in this high-dimensional setting or fundamental differences in how each method evaluates model fit given the data.
% Text: State the BIF pseudo-LL and PF approx marginal LL values. Reiterate they measure different things and AIC/BIC cannot be used for selection *between* BIF/PF here. Discuss the vast difference in values obtained – what might this imply about the different objectives, the optimization landscape, or potential issues (e.g., PF difficulty)? Reference Table~\ref{tab:empirical_overall_fit}.

\subsection{Latent States}
\label{sec:empirical:latent_states_comparison}

A direct comparison of the latent state estimates from the BIF and PF approaches provides further insight into their relative performance. Figure~\ref{fig:empirical_factors_bif} (presented earlier in Section~\ref{sec:empirical:internal_plausibility}) showed the BIF-estimated factors and log-volatilities, which exhibited clear patterns and economically interpretable dynamics. In contrast, Figure~\ref{fig:empirical_factors_pf} displays the corresponding estimates from the PF approach.

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{empirical/insample/pf/figures/filtered_states.png}
  \caption{Estimated Latent Factors and Log-Volatilities from DFSV-PF Model}
  \label{fig:empirical_factors_pf}
  \par\medskip \footnotesize Note: The figure shows estimates of the $K=5$ latent factors $\hat{\vf}_{t|T}$ (top panel) and their corresponding log-volatilities $\hat{\vh}_{t|T}$ (bottom panel) obtained from the DFSV-PF model. The estimates cover the full sample period from July 1963 to December 2023. Individual factor and log-volatility plots are provided in Appendix~\ref{app:pf_factors} and Appendix~\ref{app:pf_logvols}.
\end{figure}

The contrast between the two sets of estimates is striking. While the BIF estimates show smooth, clearly defined patterns that align with known market regimes, the PF estimates appear considerably noisier and more erratic. This excessive noise makes it difficult to discern any meaningful structure or economic interpretation from the PF-estimated states. The factors (top panel) exhibit high-frequency fluctuations that obscure any potential underlying trends, while the log-volatilities (bottom panel) show similar chaotic behavior without the clear persistence patterns visible in the BIF estimates.

This visual comparison aligns with the earlier findings regarding the PF model's poor performance in capturing average correlations (Figure~\ref{fig:empirical_avg_corr}) and its more chaotic generalized variance dynamics (Figure~\ref{fig:empirical_norm_log_gv}). The noisy nature of the PF estimates suggests that the particle filter implementation, as configured in this application, struggles to effectively extract the latent states from the high-dimensional dataset. For a more detailed examination of the individual factor and log-volatility estimates from both models, refer to Appendix~\ref{app:pf_factors} and Appendix~\ref{app:pf_logvols}, which provide separate plots for each component.

\subsection{Parameter Estimates}
\label{sec:empirical:parameter_comparison}
Table~\ref{tab:empirical_key_params_comparison} presents a direct comparison of key parameter estimates between the DFSV-BIF and DFSV-PF models. Notable differences include the higher persistence in factor dynamics for the PF model (maximum eigenvalue of $\hat{\mPhi}_f$ at 0.5974 versus 0.3324 for BIF) and the near-unit root behavior in log-volatility dynamics (maximum eigenvalue of $\hat{\mPhi}_h$ at 0.9997 for PF versus 0.9857 for BIF). The PF also estimates a higher volatility of volatilities (mean diagonal of $\hat{\mQ}_h$ at 0.0400 versus 0.0292 for BIF), which aligns with the more erratic log-volatility paths observed in Figure~\ref{fig:empirical_factors_pf}. Despite these parameter differences, both models show similar performance in residual diagnostics.
% (Optional: Include Table 5.4 from original draft here if desired) Briefly compare key estimated parameters (e.g., persistence from $\hat{\Phi}_f, \hat{\Phi}_h$) between BIF and PF. Are there noteworthy differences that might explain performance disparities?
% Optional table 5.4: Comparison of Key Parameter Estimates
% (BIF vs PF)
\begin{table}
  \centering
  \caption{Comparison of Key Parameter Estimates
(DFSV-BIF vs DFSV-PF)}
  \label{tab:empirical_key_params_comparison}
  \begin{tabular}{lcc}
    \toprule
    Parameter Description & BIF Value & PF Value
\\
    \midrule
    Max Eigenvalue($\hat{\mPhi}_f$) & 0.3324 &
0.5974 \\
    Max Eigenvalue($\hat{\mPhi}_h$) & 0.9857 &
0.9997 \\
    Mean($\hat{\vmu}$) & -6.0216 & -5.9995 \\
    Range(diag($\hat{\mSigma}_\epsilon$)) & 0.0002
- 0.0025 & 0.0003 - 0.0012 \\
    Mean(diag($\hat{\mSigma}_\epsilon$)) & 0.0006
& 0.0006 \\
    Mean(diag($\hat{\mQ}_h$)) & 0.0292 & 0.0400 \\
    \bottomrule
  \end{tabular}
  \par\medskip \footnotesize Note: Comparison of
key parameter estimates between DFSV-BIF and
DFSV-PF models. Eigenvalues indicate stationarity.
$\hat{\vmu}$ represents the unconditional mean of
log-volatilities. $\hat{\mSigma}_\epsilon$
captures idiosyncratic variances, and
$\hat{\mQ}_h$ captures the volatility of
volatilities.
\end{table}

\subsection{Residual Diagnostics}
\label{sec:empirical:residual_comparison}



Examining the residual diagnostic results presented in Table~\ref{tab:empirical_resid_pass_rates}, we compare the performance of the DFSV-BIF and DFSV-PF models in addressing remaining serial correlation and conditional heteroskedasticity. As highlighted in Section~\ref{sec:empirical:residual_puzzle}, both methods exhibit significant shortcomings in this regard, particularly when compared to the benchmark DCC-GARCH model. The pass rates for the Ljung-Box Q test on squared residuals and the ARCH-LM test remain low for both DFSV specifications, generally below 20\%. While Table~\ref{tab:empirical_resid_pass_rates} indicates slightly higher pass rates for the DFSV-PF compared to the DFSV-BIF across most tests (e.g., 17.89\% vs 15.79\% for Q(5), 16.84\% vs 14.74\% for ARCH-LM(5)), these differences are marginal. The key finding remains that neither the BIF nor the PF estimation approach, applied to the specified DFSV model, successfully resolves the conditional heteroskedasticity present in the portfolio returns according to these standard univariate diagnostics.

\subsection{Computation \& Stability}
\label{sec:empirical:computation_comparison}

Finally, we compare the computational performance and stability of the BIF and PF estimation methods in this empirical application, referring back to the summary statistics in Table~\ref{tab:empirical_estimation_summary}. A significant difference in computational cost was observed: the DFSV-BIF required approximately 175 minutes for estimation, whereas the DFSV-PF completed in just under 29 minutes. This finding contrasts somewhat with the simulation results presented in Chapter~\ref{ch:simulation}, where the BIF often exhibited more favorable scaling properties, particularly in higher dimensions. The empirical difference could stem from various factors, including the specific implementation details, the complexity of the pseudo-likelihood evaluation and its gradients within each BIF iteration compared to the particle propagation and weighting in the PF, or the particular convergence path taken by the optimizers on this dataset. Both methods successfully converged according to the optimizer's criteria, although the PF required more iterations (466) than the BIF (289). While the PF demonstrated potential instability in some simulation scenarios (Chapter~\ref{ch:simulation}), it achieved convergence without apparent issues in this specific empirical run, albeit yielding less plausible state estimates as discussed previously.
% Refer back to Table 5.1. Compare their empirical estimation time and convergence. Discuss how this aligns with or contrasts with the simulation findings (Chapter 4) regarding scalability and robustness trade-offs. Reference Table~\ref{tab:empirical_estimation_summary}.

\subsection{Synthesis of BIF vs. PF Comparison}
\label{sec:empirical:synthesis_bif_pf}

In summary, this direct comparison of the Bellman Information Filter and Particle Filter for estimating the DFSV model on the Fama-French 95 dataset reveals distinct empirical trade-offs. The BIF yielded considerably more interpretable latent state estimates (Figure~\ref{fig:empirical_factors_bif}) compared to the noisy and erratic states produced by the PF (Figure~\ref{fig:empirical_factors_pf}), despite the PF estimating slightly higher persistence parameters (Table~\ref{tab:empirical_key_params_comparison}). However, this interpretability came at a significant computational cost, with the BIF requiring substantially more estimation time than the PF in this application (Table~\ref{tab:empirical_estimation_summary}). Regarding model adequacy, both methods struggled significantly with residual diagnostics compared to the benchmark DCC-GARCH model, failing to eliminate conditional heteroskedasticity according to standard tests (Table~\ref{tab:empirical_resid_pass_rates}), although the PF showed marginally better pass rates. Finally, assessing the overall statistical fit remains challenging due to the fundamentally different likelihood objectives optimized by each method; the BIF achieved a vastly higher pseudo-log-likelihood value, while the PF produced a much lower approximate marginal log-likelihood (Table~\ref{tab:empirical_overall_fit}), highlighting the difficulty in directly comparing these approaches based solely on their optimized criteria.
% Interpret the Puzzle: Elaborate on potential reasons for the discrepancy between plausible $\hat{h}_t$ and poor ARCH residuals for DFSV models (especially BIF). Consider:
%     * Model Specification: Is VAR(1) for $h_t$ insufficient? Is the diagonal $Q_h$ too restrictive? Is the linear factor loading $\Lambda$ inadequate?
%     * Estimation/Approximation: Does the BIF's mode-based/Gaussian approximation limit its ability to capture the tail behavior relevant for ARCH tests? Did the optimization converge to a point good for the pseudo-LL but poor for residuals?
%     * Data Issues: Is the signal-to-noise ratio low? Are factor dynamics weakly linked to observation volatility in this dataset?
% Evaluate DFSV-BIF: Based on the *combined* evidence, provide a nuanced assessment of the DFSV-BIF approach in this empirical context. What are its demonstrated strengths and weaknesses?
% Implications: Discuss the practical relevance. If ARCH residuals are poor, how useful is the model for risk forecasting or tasks sensitive to volatility dynamics? How does it compare pragmatically to DCC-GARCH for this dataset?
% Link to Research Questions: Explicitly revisit the initial research questions (from Chapter 1 / Section 5.1) and discuss how the empirical findings answer, or complicate, them.