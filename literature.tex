\chapter{Literature Review}
This chapter provides a comprehensive review of the existing literature related to the study. It begins with an examination of factor models in asset pricing and macro-finance, followed by multivariate stochastic volatility modeling. The chapter then reviews state-space filtering techniques, parameter estimation approaches in latent state models, and positions the Bellman filter approach within the DFSV estimation landscape. The aim is to contextualize the current study within established theoretical and empirical frameworks, identifying gaps and opportunities for further investigation.

\section{Factor Models in Asset Pricing and Macro-Finance}

Latent factor models have a long history in finance for explaining asset returns. Early examples include Ross's Arbitrage Pricing Theory and the Fama-French multi-factor models, which use static factors to summarize cross-sectional return drivers. Over time, researchers extended factor models to account for dynamics in the factors and their impact on returns.
For example, \citeA{borghi_dynamics_2018} develop a two-level factor model with time-varying factor loadings and apply it to 1,815 global equities. They find that exposure ("beta") to global risk factors increases during worldwide shocks and that the persistence of these loadings depends on firm characteristics (e.g. larger firms exhibit less persistent factor exposures). Such results underscore that static factor models (like the Fama-French framework) may miss important temporal variation in risk exposures.\\
% TODO: Expand on Bayesian dynamic factor models that allow factor loadings or factor realizations to evolve over time
% Draft text: For instance, Bayesian dynamic factor models have been developed to allow factor loadings or factor realizations to evolve. \\

\citeA{aguilar_bayesian_2000} introduced a Bayesian dynamic factor model with stochastic volatility for portfolio allocation, demonstrating improved modeling of time-varying risks in exchange rates. Their work showed that incorporating stochastic volatility in factor structures can enhance short-term forecast accuracy and portfolio decisions. Building on such ideas, \citeA{han_asset_2006} proposed a Dynamic Factor Multivariate Stochastic Volatility (DFMSV) model that allows both the expected returns and volatilities of a large number of assets to vary over time. In this model, a few latent factors capture the common movements in returns, and distinctively, each factor is assumed to follow an autoregressive process with time-varying volatility. This extension is motivated by empirical evidence that many economic predictors (e.g. dividend yields, interest rates) exhibit persistence, justifying AR(1) factor dynamics. \citeA{han_asset_2006} finds that allowing factors to have stochastic volatilities and autocorrelations significantly improves the model's realism and its usefulness for asset allocation decisions.\\

\section{Multivariate Stochastic Volatility Modeling}

Modeling time-varying volatility (heteroskedasticity) in multiple assets is challenging due to the curse of dimensionality. Traditional multivariate GARCH models quickly become overparameterized beyond a few series \cite{bollerslev_chapter_1994}. A breakthrough came with Engle's Dynamic Conditional Correlation (DCC) model, which simplifies the covariance dynamics by modeling correlations separately from variances \cite{engle_dynamic_2000}. The DCC model allows each asset's variance to follow a univariate GARCH process while the correlation matrix evolves in a parsimonious manner, making estimation feasible for dozens of series. This approach has been widely used in finance for its balance of flexibility and simplicity.\\

Stochastic volatility (SV) models provide an alternative by treating volatilities as latent variables following stochastic processes (often AR(1) in log-volatility). Multivariate SV models were explored by \citeA{harvey_multivariate_1994} and \citeA{jacquier_stochastic_1999} for small dimensions. To scale SV models to higher dimensions, researchers imposed factor structures. \citeA{philipov_factor_2006}, for example, proposed a factor SV model where the covariance of factor returns follows a Wishart process. This Wishart multivariate SV framework allows a flexible yet structured evolution of the covariance matrix and demonstrated the ability to handle on the order of tens of assets by reducing effective dimensionality. Similarly, \citeA{yu_multivariate_2006} compared various multivariate SV specifications, finding that factor SV models often perform well in capturing co-movements. \citeA{asai_multivariate_2006} provide a comprehensive review of multivariate SV models, categorizing them into factor models, models with time-varying correlations, and others.\\

The factor-based SV models seem like a promising approach for modeling high-dimensional volatility. By assuming a few latent drivers for volatility (e.g., a market volatility factor, sector volatility factors, etc.), these models drastically reduce the number of parameters and mitigate estimation noise. The DFMSV model of \citeA{han_asset_2006} is a prime example: it showed that with only a handful of latent factors, one can model the covariances of dozens of assets, something infeasible with unrestricted multivariate SV or GARCH. However, the complexity of such models (latent factors + latent time-varying variances) makes estimation a bottleneck. Early applications resorted to Bayesian MCMC estimation (which is simulation-intensive) or variational approximations. There remains a need for more efficient filtering and parameter estimation techniques to unlock the potential of high-dimensional SV models in practice.\\

Building on these foundations, more recent research has tackled the high-dimensional estimation challenges that come with dynamic factor SV models. Bayesian techniques are common: \citeA{mccausland_dynamic_2015} and \citeA{kastner_efficient_2017}, develop efficient Bayesian simulation methods that dramatically speed up estimation in factor SV models. By employing clever posterior simulation tricks (such as interweaving strategies to improve Markov chain Monte Carlo mixing), they report order-of-magnitude faster convergence without sacrificing accuracy. This makes it feasible to estimate factor volatility models on larger panels of assets (e.g. dozens of currencies or stocks) that were previously computationally prohibitive.\\

\section{State-Space Filtering Techniques: A Review}

% TODO: Write general introduction to state-space filtering techniques, covering basic concepts, historical development, and importance in time series analysis

\subsection{Kalman-Based Filters (KF, EKF, UKF) and their Limitations for DFSV}

In linear Gaussian state-space models, the classical approach is to use maximum likelihood via the Kalman filter and EM algorithm. Shumway and Stoffer (1982) pioneered the use of the EM algorithm with Kalman smoothing to iteratively find ML estimates of parameters \cite{shumway_approach_1982}. Their method exploits the tractability of the linear Gaussian case, where the E-step computes expected sufficient statistics using Kalman smoother outputs, and the M-step updates parameter estimates analytically. This approach remains a workhorse for, e.g., linear dynamic factor models without stochastic volatility.\\

For non-linear or non-Gaussian models (such as those with stochastic volatility), parameter estimation is more challenging. Extended Kalman Filters (EKF) and Unscented Kalman Filters (UKF) can provide on-line estimates of parameters by augmenting the state vector or by linearizing the state-space model, then applying ML or least squares on the residuals. These methods are faster but introduce bias if the linearization is crude. Moreover, EKF/UKF still assume certain model structures (e.g., additive Gaussian noise) and can perform poorly when those assumptions are violated \cite{lange_bellman_2024}.\\

\subsection{Simulation-Based Filters: Particle Filters (PF/SMC) and the Curse of Dimensionality}

Particle filtering (PF) offers a general solution by simulating many particles (state trajectories) and updating weights to approximate the filtering distribution. Particle filters can also estimate static parameters either by treating them as part of the state or via separate algorithms (like particle learning or particle EM). While very flexible, particle methods are notoriously resource-intensive for parameter estimation. The literature includes techniques like particle Markov-chain Monte Carlo and particle smoothing for likelihood evaluation. \citeA{malik_modelling_2011} developed a particle filter approach for parameter estimation in more complex, non-gaussian SV models, but their method is practical only for low-dimensional cases (e.g. a single volatility process). In general, as the state dimension grows, particle filters suffer from weight degeneracy and require exponentially many particles to maintain accuracy.\\

Recent research has explored more efficient filtering approaches. Numerically accelerated importance sampling (NAIS) methods (e.g. \cite{koopman_numerically_2015}) use analytical approximations to guide the particle filter, improving efficiency in moderate dimensions. Approximations of the likelihood via Laplace methods or variational Bayes have also been considered for SV models. However, these either compromise some accuracy or still struggle when the model has many latent variables.\\

\subsection{Bellman Filtering: Mode-Based Estimation and the Information Filter Variant}

Bellman filtering is a recently developed method inspired by Bellman's dynamic programming principle for optimal control. In the context of state-space models, it recasts the filtering update as a sequential optimization problem rather than integration. At each time step, Bellman filtering seeks the mode of the posterior distribution of the state (or a quadratic approximation of the value function) by combining the prior (prediction from the previous state) with the new observation likelihood (\cite{lange_bellman_2024}). This approach can handle non-linear and non-Gaussian features by effectively performing a proximal optimization step at each update. \citeA{lange_bellman_2024} shows that the Bellman filter generalizes the iterated Extended Kalman Filter to allow arbitrary observation and transition densities, while maintaining polynomial computational complexity. Notably, it avoids particle simulations, instead using a quadratic approximation to the log-posterior (similar in spirit to a Laplace approximation) at each time step.\\

The performance advantages reported for Bellman filtering are significant. First, it is computationally efficient, scaling on the order of $O(m^3)$ for state dimension $m$, similar to the Kalman filter. This makes it feasible for state vectors of size up to hundreds, which is often the case in factor models (e.g., a model with 10 factors and 100 idiosyncratic volatilities has a state dimension $m \approx 110$). In Lange's empirical studies, the Bellman filter was successfully applied to problems with $m \approx 150$. Second, it remains accurate despite the approximations: in simulation experiments for non-linear models, Bellman filtering achieved state estimation accuracy on par with particle filtering and other simulation-based methods, yet at a small fraction of the computational cost. In other words, it offers an excellent speed-accuracy trade-off, essentially bringing high-dimensional Bayesian filtering into a more practical regime.\\

Another advantage is stability. The Bellman filter update is shown to be a contraction in mean-square error under certain conditions, preventing the filter from diverging as time progresses. It also exhibits an invertibility property: the influence of the initial state guess decays exponentially fast. These properties are crucial when dealing with long financial time series, ensuring that estimation errors do not blow up over time – a common concern with naive implementations of EKF or particle filters.\\

\section{Parameter Estimation in Latent State Models}

% TODO: Write general introduction to parameter estimation in latent state models, covering key challenges, common approaches, and importance in financial applications

\subsection{Bayesian (MCMC) vs. Frequentist (ML, PML, Simulation-Based) Approaches}

Estimating hyperparameters (such as the well known $ABCD$ matrices in the standard state-space formulation) is a critical step in implementing state-space models. One strategy is Bayesian estimation, treating hyperparameters as random and using MCMC or particle MCMC to sample from the joint posterior. \citeA{kim_stochastic_1998} and others applied Bayesian methods to univariate SV models successfully. However, fully Bayesian approaches can be very slow for high-dimensional problems.\\

Another strategy is to approximate the likelihood and use direct optimization (a quasi-ML approach). These frequentist approaches often rely on approximations to the likelihood function, which can be maximized using standard optimization techniques. While potentially faster than full Bayesian approaches, they may sacrifice some accuracy or robustness.\\

\subsection{Estimation Challenges: Likelihood Intractability, Identification, and Computational Cost}

In summary, conventional hyperparameter estimation techniques either scale poorly with dimension (MCMC, particle filters) or rely on simplifying assumptions (EKF/UKF, analytic approximations). This motivates exploring the Bellman filtering approach, which constructs a pseudo-likelihood during filtering that can be optimized for parameter estimates (\cite{lange_bellman_2024}). By circumventing heavy sampling and focusing on modes of the filtering distribution, Bellman filtering promises a faster route to hyperparameter identification, which is particularly appealing for high-dimensional, complex models.\\

\section{Positioning the Bellman Filter Approach within the DFSV Estimation Landscape}

Compared to the Extended Kalman Filter (EKF) and Unscented Kalman Filter (UKF), Bellman filtering does not assume Gaussian noise or rely on local linearization, which makes it more robust in cases of model misspecification or observation outliers. Unlike particle filters, it deterministically approximates the posterior, avoiding random Monte Carlo error and the need for a very large number of particles in high dimensions. Additionally, Bellman filtering naturally yields a pseudo-likelihood as a byproduct of its recursive updates. This pseudo-likelihood is exact for linear-Gaussian models and a second-order accurate approximation for general models. It can be maximized with respect to static parameters using standard optimization algorithms, thereby tackling the hyperparameter estimation problem in a quasi-maximum likelihood framework. This is a significant improvement over particle filters, which often require custom likelihood estimators or Bayesian approaches for parameter learning.\\

% TODO: Complete this concluding paragraph summarizing how Bellman filtering combines strengths of Kalman and particle filtering, highlighting its advantages for DFSV models and the novelty of applying it in financial econometrics
% Draft text: In summary, Bellman filtering combines the strengths of Kalman and particle filtering: it is fast and scalable like the Kalman filter, yet flexible and general like particle filtering. By outperforming competing methods in both accuracy and speed for challenging cases (e.g., highly non-linear or high-dimensional systems), it provides an attractive tool for estimating dynamic factor stochastic volatility models, which have exactly those challenges. This research will be among the first to apply Bellman filtering in a financial econometrics context, evaluating its performance advantages in modeling asset return dynamics.
