\chapter{Empirical Application: Factor Dynamics in Financial Markets using the Bellman Information Filter}
\label{ch:empirical_application}

% Chapter Introduction (Revised)
This chapter applies the Dynamic Factor Stochastic Volatility (DFSV) model, estimated using the Bellman Information Filter (BIF), to 95 Fama-French portfolio returns (July 1963–December 2023). The primary objective is to evaluate its ability to capture dynamic factor structures and the resulting financial co-movement in high-dimensional data. The analysis reveals a key tension: the DFSV-BIF framework yields plausible aggregate dynamics and co-movement patterns, suggesting success in capturing systematic risk, but struggles with conditional heteroskedasticity in individual returns, highlighting a limitation in capturing full asset-specific volatility.
\section{Model Specifications and Estimation Summary}
\label{sec:empirical:specification_estimation}

% Model Recap: Briefly present the core equations defining the DFSV, DFM, and DCC models (referencing Chapters 2/3 for full details). Specify key dimensions (N=95, K=5). Briefly note the nature of the likelihood optimized for each: pseudo-likelihood for BIF, approximate marginal likelihood for PF, standard marginal likelihood for DFM and DCC.
This section recaps the models and estimation procedures I employ in this empirical analysis. I compare four models applied to the $N=95$ Fama-French portfolio returns:
\begin{enumerate}
    \item The Dynamic Factor Stochastic Volatility (DFSV) model, detailed in Chapter~\ref{ch:methodology}, featuring $K=5$ latent factors with time-varying volatility. I estimate this model using two methods:
    \begin{itemize}
        \item \textbf{DFSV-BIF:} Maximizing the pseudo-log-likelihood via the Bellman Information Filter (Section~\ref{sec:bif_filter}).
        \item \textbf{DFSV-PF:} Maximizing an approximate marginal log-likelihood via a Particle Filter (Section~\ref{sec:particle_filter}) with $P=10,000$ particles.
    \end{itemize}
    \item A benchmark Dynamic Factor Model (DFM) with constant volatility, which I estimate via standard Maximum Likelihood using the \texttt{statsmodels} Python package.
    \item A benchmark DCC-GARCH(1,1) model, which I estimate via standard Maximum Likelihood using the \texttt{arch} and \texttt{mgarch} Python packages.
\end{enumerate}
I estimate all models using the full sample period from July 1963 to December 2023.
% Estimation Overview: Summarize the full in-sample (1963-end) estimation approach. Mention the estimation method/software used for each (BIF optimization, PF optimization, statsmodels for DFM, 'arch'/'rmgarch' for DCC). Note key settings like BIF/PF optimizers and PF particle count (e.g., P=10,000).

\subsection{Computational Performance}
% Computational Performance Introduction
I first compare the computational resources estimation requires. Table~\ref{tab:empirical_estimation_summary} details the wall-clock time, convergence status, and optimizer iterations.
\label{sec:empirical:computational_performance}

% Table 5.1: Estimation Summary (Wall Clock Time, Convergence Success, Iterations).
\begin{table}[htbp]
  \centering
  \caption{Estimation Summary: Computational Performance and Stability (July 1963–Dec 2023). Convergence success indicates successful termination of the optimization procedure. Iterations refer to the number required by the optimizer, where applicable.}
  \label{tab:empirical_estimation_summary}
  \begin{tabular}{lccc}
    \toprule
    Model       & Estimation Time (Wall Clock) & Convergence Success & Iterations \\
    \midrule
    DFSV-BIF    & 175.18 min                        & Yes                 & 289        \\
    DFSV-PF     & 28.75 min                          & Yes                 & 466        \\
    DFM         & 4.53 min                      & Yes                 & 137        \\
    DCC-GARCH   & 4.86 min                      & Yes                 & N/A        \\
    \bottomrule
  \end{tabular}
\end{table}

% Text: Briefly discuss the observed computational times and convergence status[cite: 1092]. Note the empirical runtime differences (e.g., BIF being slower than PF/DCC/DFM in this application).
All optimizations converged successfully. Table~\ref{tab:empirical_estimation_summary} reveals significant estimation time differences: DFSV-BIF required the most time (approx. 175 min), followed by DFSV-PF (approx. 29 min). Both were substantially slower than DFM and DCC-GARCH (< 5 min each), reflecting the computational burden of estimating latent stochastic volatility. Interestingly, BIF required fewer optimizer iterations than PF despite its longer runtime, suggesting differences in iteration complexity or the optimization landscape encountered.

\section{The DFSV-BIF Model: Internal Plausibility}
\label{sec:empirical:internal_plausibility}

Before comparing models, I assess the internal plausibility of DFSV-BIF by examining if the estimated parameters and latent states, which represent the drivers of co-movement, align with economic intuition and established stylized facts.

Table~\ref{tab:empirical_key_params_bif} summarizes key DFSV-BIF parameter estimates governing the co-movement dynamics. Both transition matrices satisfy stationarity conditions: the maximum absolute eigenvalue of $\hat{\mPhi}_f$ (0.3324) implies low persistence in the latent factors themselves, while the maximum absolute eigenvalue of $\hat{\mPhi}_h$ (0.9857) indicates high persistence in their log-volatilities, consistent with the well-documented phenomenon of volatility clustering often observed in systematic risk components.

\begin{table}[htbp]
  \centering
  \caption{Summary of Key Parameter Estimates (DFSV-BIF). Eigenvalues indicate stationarity. $\hat{\vmu}$ represents the unconditional mean of log-volatilities. $\hat{\mSigma}_\epsilon$ captures idiosyncratic variances, and $\hat{\mQ}_h$ captures the volatility of volatilities.}
  \label{tab:empirical_key_params_bif}
  \begin{tabular}{lc}
    \toprule
    Parameter Description & Value / Range \\
    \midrule
    Max Eigenvalue($\hat{\mPhi}_f$) & 0.3324 \\
    Max Eigenvalue($\hat{\mPhi}_h$) & 0.9857 \\
    Mean($\hat{\vmu}$) & -6.0216 \\
    Range(diag($\hat{\mSigma}_\epsilon$)) & 0.0002–0.0025 \\
    Mean(diag($\hat{\mSigma}_\epsilon$)) & 0.0006 \\
    Mean(diag($\hat{\mQ}_h$)) & 0.0292 \\
    \bottomrule
  \end{tabular}
\end{table}

Figure~\ref{fig:empirical_heatmaps_phi} visualizes these transition matrices governing the evolution of the co-movement drivers. The heatmaps confirm the dynamics implied by the eigenvalues: weaker diagonal persistence in $\hat{\mPhi}_f$ contrasts with strong diagonal persistence in $\hat{\mPhi}_h$. Off-diagonal elements suggest some cross-factor interaction in $\hat{\mPhi}_f$ but minimal log-volatility spillover in $\hat{\mPhi}_h$, indicating that the volatility dynamics of the different sources of common variation are largely independent.

\begin{figure}[htbp]
  \centering
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/phi_f_heatmap.png}
    \caption{Factor transition matrix ($\hat{\mPhi}_f$)}
    \label{fig:empirical_heatmap_phi_f}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{empirical/insample/bif/figures/phi_h_heatmap.png}
    \caption{Log-volatility transition matrix ($\hat{\mPhi}_h$)}
    \label{fig:empirical_heatmap_phi_h}
  \end{subfigure}
  \caption{Heatmaps of Estimated Transition Matrices from DFSV-BIF. The panels visualize the estimated transition matrices for latent factors ($\hat{\mPhi}_f$, left) and log-volatilities ($\hat{\mPhi}_h$, right). Diagonal elements indicate persistence, while off-diagonal elements capture cross-factor dependencies.}
  \label{fig:empirical_heatmaps_phi}
\end{figure}

Other parameters reported in Table~\ref{tab:empirical_key_params_bif} are economically reasonable. The mean unconditional latent factor log-volatility ($\hat{\vmu}=-6.02$) implies a plausible unconditional monthly factor standard deviation (approximately 4.9\%). The range and average of the diagonal elements of $\hat{\mSigma}_\epsilon$ imply sensible non-systematic risk levels (corresponding to 1.4\%-5.0\% monthly standard deviation) relative to the total standard deviations observed in portfolio returns. Furthermore, the mean diagonal element of $\hat{\mQ}_h$ allows for plausible variation in the volatility of the factors driving co-movement over time.

The estimated factor loadings $\hat{\mLambda}$, visualized in Figure~\ref{fig:empirical_heatmap_lambda}, further support the model's ability to capture sources of co-movement. The heatmap reveals a distinct block structure that aligns well with the known portfolio sorting characteristics (Size and Book-to-Market), indicating that the estimated factors successfully capture relevant systematic risk dimensions driving common variation across portfolios, as intended by the model specification.

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{empirical/insample/bif/figures/lambda_heatmap.png}
  \caption{Heatmap of Estimated Factor Loadings ($\hat{\mLambda}$) from DFSV-BIF on FF-95 Portfolios. The heatmap displays the estimated loadings of the $N=95$ Fama-French portfolios (sorted by Size and Book-to-Market, y-axis) on the $K=5$ latent factors (x-axis). Loadings are identified via the lower-triangular constraint discussed in Section~\ref{sec:identification}.}
  \label{fig:empirical_heatmap_lambda}
\end{figure}

Finally, the smoothed latent states presented in Figure~\ref{fig:empirical_factors_bif}, representing the estimated sources of common variation and their volatility, appear reasonable. The estimated latent factors ($\hat{\vf}_{t|T}$, top panel) exhibit varying levels of activity over the sample period, with Factor 1 clearly reflecting market stress periods such as the Global Financial Crisis (GFC). Concurrently, the estimated latent factor log-volatilities ($\hat{\vh}_{t|T}$, bottom panel) exhibit high persistence and pronounced spikes during market crises, visually confirming the model's ability to capture the clustering behavior characteristic of systematic volatility driving co-movement.

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{empirical/insample/bif/figures/filtered_states.png}
  \caption{Estimated Latent Factors and Log-Volatilities from DFSV-BIF Model (July 1963–Dec 2023). The figure shows smoothed estimates of the $K=5$ latent factors $\hat{\vf}_{t|T}$ (top panel) and their corresponding log-volatilities $\hat{\vh}_{t|T}$ (bottom panel). Individual factor and log-volatility plots are provided in Appendix~\ref{app:empirical_supplementary}.}
  \label{fig:empirical_factors_bif}
\end{figure}

In summary, the DFSV-BIF parameter estimates and the resulting latent states appear econometrically sound and align well with established stylized facts and economic intuition regarding the drivers of co-movement. This suggests the model possesses internal consistency and effectively captures key dynamic features related to common variation in the financial market data.

\section{Comparative Analysis of Market Dynamics}
\label{sec:empirical:comparative_dynamics}

I now compare how the different estimated models capture aggregate market risk and co-movement dynamics. This comparison focuses on the evolution of overall variance and correlation derived from the estimated conditional covariance matrix, $\widehat{\mSigma}_t^{(m)}$ (defined in Equation~\ref{eq:def_cond_cov}), which primarily reflects the factor-driven components. Figure~\ref{fig:empirical_covariance_dynamics} illustrates key metrics derived from these matrices.
% Objective: Compare how the different models capture aggregate, system-wide dynamics reflected in the implied covariance matrix over time.

\subsection{Covariance Dynamics (Overall Variance \& Correlation)}
\label{sec:empirical:covariance_dynamics}

% Figures comparing covariance dynamics metrics
\begin{figure}[htbp]
  \centering
  % First row: Log Generalized Variance and Normalized Log Generalized Variance
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{empirical/insample/comparison/log_determinant_comparison.pdf}
    \caption{Log Generalized Variance ($\log |\hat{\mSigma}_t|$)}
    \label{fig:empirical_log_gv}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{empirical/insample/comparison/normalized_log_determinant_comparison.pdf}
    \caption{Normalized Log Generalized Variance}
    \label{fig:empirical_norm_log_gv}
  \end{subfigure}

  \vspace{1em} % Add vertical space between rows

  % Second row: Average Conditional Correlation (centered)
  \begin{subfigure}[b]{0.7\textwidth} % Adjusted width for better centering
    \centering
    \includegraphics[width=\textwidth]{empirical/insample/comparison/average_correlation_comparison.pdf}
    \caption{Average Conditional Correlation ($\bar{\rho}_t$)}
    \label{fig:empirical_avg_corr}
  \end{subfigure}

  \caption{Comparative Time Series of Covariance Dynamics Metrics (July 1963–Dec 2023). Comparison of key covariance metrics derived from the estimated conditional covariance matrix $\widehat{\mSigma}_t^{(m)}$ (Equation~\ref{eq:def_cond_cov}) for DFSV-BIF, DFSV-PF, DFM (Factor-CV), and DCC-GARCH models. Panel (a) shows Log Generalized Variance ($\log |\hat{\mSigma}_t|$), reflecting overall systemic variance. Panel (b) shows Normalized Log Generalized Variance (standardized), highlighting the timing and relative intensity of volatility dynamics. Panel (c) shows Average Conditional Correlation ($\bar{\rho}_t$), measuring market co-movement.}
  \label{fig:empirical_covariance_dynamics}
\end{figure}

% This sentence is redundant with the section introduction above line 119 and is removed.

Figure~\ref{fig:empirical_log_gv} presents the Log Generalized Variance ($\log |\hat{\mSigma}_t|$), a measure of overall systemic variance reflecting the dispersion captured by the model's covariance matrix. The DFM, by construction, exhibits a constant level due to its static covariance assumption. In contrast, DFSV-BIF, DFSV-PF, and DCC-GARCH all capture pronounced time variation, characterized by sharp spikes during market crises (e.g., GFC, COVID-19 pandemic), reflecting periods of heightened market volatility driven primarily by common factors. Baseline variance levels differ across models, with DCC-GARCH implying generally higher systemic variance than the DFSV models.

Figure~\ref{fig:empirical_norm_log_gv} displays the Normalized Log Generalized Variance, allowing comparison of dynamic patterns irrespective of baseline levels. DFSV-BIF and DCC-GARCH exhibit remarkably similar timing and relative magnitude of volatility fluctuations. This suggests broad agreement between these two structurally different models regarding the identification of volatility regimes. In contrast, the dynamics implied by DFSV-PF appear more chaotic and less clearly linked to known market events, indicating a potentially weaker capture of aggregate market volatility dynamics by the PF implementation used here.

Figure~\ref{fig:empirical_avg_corr} illustrates the Average Conditional Correlation ($\bar{\rho}_t$), a direct measure of captured market co-movement derived from the off-diagonal elements of the estimated conditional correlation matrix. Both DFSV-BIF and DCC-GARCH exhibit dynamically varying correlations, which typically increase during periods of market stress, consistent with established stylized facts about co-movement. The DFM implies a constant average correlation. Strikingly, the DFSV-PF yields an extremely low and virtually flat average correlation throughout the sample period. This suggests a potential failure of the PF estimation, as implemented here, to capture the time-varying market co-movement structure, highlighting a possible weakness or sensitivity of the PF approach in this high-dimensional setting.


\section{The Residual Diagnostics Puzzle}
\label{sec:empirical:residual_puzzle}

% Objective: Evaluate the models based on standard time series diagnostics applied to standardized residuals, explicitly addressing the conflict with the seemingly plausible results from Section 5.3.

Having established the internal plausibility of the DFSV-BIF model's estimates in Section~\ref{sec:empirical:internal_plausibility}, I now evaluate its performance using standard time series diagnostics applied to its standardized residuals. The goal is to determine if these residuals adequately approximate the white noise process assumed by the model specification.
\subsection{Diagnostic Test Results}
\label{sec:empirical:diagnostic_results}

Standard model evaluation often involves examining standardized residuals, defined as $\hat{\vz}_t^{(m)} = (\widehat{\mSigma}_t^{(m)})^{-1/2} (\vr_t - \widehat{\mLambda}^{(m)} \hat{\vf}_t^{(m)})$, for any remaining serial correlation or conditional heteroskedasticity. Ideally, these residuals should behave like independent and identically distributed (i.i.d.) noise. Table~\ref{tab:empirical_resid_pass_rates} summarizes the results of univariate diagnostic tests applied to each of the $N=95$ standardized residual series generated by the four competing models.

The diagnostic tests reveal stark performance differences among the models. The DCC-GARCH model successfully eliminated ARCH effects (as measured by ARCH-LM tests) in nearly 99\% of the individual residual series, far exceeding the performance of the DFSV variants (Table~\ref{tab:empirical_resid_pass_rates}). Similarly, for the Ljung-Box Q-statistic applied to squared residuals (testing for remaining autocorrelation in variance), DCC-GARCH achieved an 88.42\% pass rate (at lag 10), compared to only 9.47\% for DFSV-BIF. This strongly suggests that the DCC-GARCH specification captures the volatility clustering observed in individual asset returns much more effectively than the DFSV models.

Surprisingly, both DFSV variants performed poorly on these diagnostics, with pass rates generally falling below 20\%. For the ARCH-LM test with 5 lags, DFSV-BIF (9.47\% pass rate) performed even worse than the constant-volatility DFM (21.05\%). This finding suggests that the complex factor stochastic volatility structure within the DFSV model, as specified here, contributes little towards capturing the conditional heteroskedasticity present in individual asset returns beyond what is captured by the factors themselves. As expected given the well-known heavy tails of financial return distributions, all models failed the Jarque-Bera normality tests for nearly all residual series.
% Table 5.4: Univariate Residual Diagnostic Pass Rates (p > 0.05) (Ljung-Box Q(Squared), ARCH-LM, Jarque-Bera).
\begin{table}[htbp]
  \centering
  \caption{Univariate Residual Diagnostic Pass Rates ($p > 0.05$). Percentage of the N=95 standardized residual series for which the null hypothesis (no serial correlation in squared residuals, no ARCH effects, normality) is not rejected at the 5\% significance level. Higher percentages indicate better model specification regarding these aspects.}
  \label{tab:empirical_resid_pass_rates}
  \begin{tabular}{lcccc}
    \toprule
    Test (Lag)                     & DFSV-BIF & DFSV-PF & DFM & DCC-GARCH \\
    \midrule
    Ljung-Box Q(5) (Squared)       & 7.37\%   & 11.58\% & 10.53\% & 88.42\% \\
    Ljung-Box Q(10) (Squared)      & 9.47\%   & 15.79\% & 13.68\% & 88.42\% \\
    Ljung-Box Q(15) (Squared)      & 10.53\%  & 12.63\% & 7.37\%  & 88.42\% \\
    Ljung-Box Q(20) (Squared)      & 12.63\%  & 17.89\% & 17.89\% & 88.42\% \\
    ARCH-LM(5)                     & 9.47\%   & 12.63\% & 21.05\% & 98.95\% \\
    ARCH-LM(10)                    & 11.58\%  & 23.16\% & 28.42\% & 98.95\% \\
    Jarque-Bera                    & 2.11\%   & 0.00\%  & 0.00\%  & 0.00\%  \\
    \bottomrule
  \end{tabular}
\end{table}

\subsection{Framing the Puzzle}
\label{sec:empirical:framing_puzzle}

The empirical results presented thus far constitute a puzzle. On one hand, the DFSV-BIF estimation produced plausible results regarding the drivers of co-movement: sensible parameter estimates, a clear factor loading structure consistent with economic priors (Figure~\ref{fig:empirical_heatmap_lambda}), and estimated latent states that align well with major market events and crises (Figure~\ref{fig:empirical_factors_bif}). Furthermore, it successfully captured plausible aggregate dynamics and co-movement patterns (variance and correlation) in a manner broadly similar to the DCC-GARCH model (Section~\ref{sec:empirical:comparative_dynamics}). On the other hand, standard diagnostic tests applied to the individual standardized residuals revealed high failure rates (often exceeding 90\%) for tests related to conditional heteroskedasticity (Table~\ref{tab:empirical_resid_pass_rates}). Why does a model that appears to capture market-wide co-movement dynamics reasonably well perform so poorly on standard tests designed to detect residual volatility dynamics at the individual asset level, pointing towards limitations in capturing full asset-specific volatility?
% Text: Present the findings directly and clearly[cite: 221, 376]:
%     * State the strong performance of DCC-GARCH in eliminating ARCH effects (high pass rates).
%     * State the poor performance of DFSV-BIF and DFSV-PF on ARCH tests (low pass rates), noting they are often no better than the constant-volatility DFM.
%     * Mention the failure of all models on normality tests (Jarque-Bera). Reference Table~\ref{tab:empirical_resid_pass_rates}.
\subsection{Diagnosing Residual Heteroskedasticity via GARCH Modelling}
\label{sec:garch_analysis} % Optional: Add a label for cross-referencing the subsection
To investigate whether the poor diagnostic performance stems from unmodeled heteroskedasticity in the idiosyncratic errors (specifically, the assumption of a constant idiosyncratic covariance matrix $\mSigma_{\epsilon}$), I conduct a further diagnostic exercise. I fit univariate GARCH(1,1) models to each of the 95 standardized residual series obtained from the DFSV-BIF estimation. Table~\ref{tab:empirical_garch_params} summarizes the key findings from this analysis.

\begin{table}[htbp]
  \centering
  \caption{GARCH(1,1) Parameter Analysis for DFSV-BIF Residuals. Results from fitting GARCH(1,1) models (with Student's t distribution) to each of the 95 standardized residual series from the DFSV-BIF model. $\alpha_1$ and $\beta_1$ refer to the ARCH and GARCH parameters, respectively. The Wald test examines $H_0: \alpha_1 + \beta_1 = 0$ against $H_1: \alpha_1 + \beta_1 \neq 0$.}
  \label{tab:empirical_garch_params}
  \begin{tabular}{lc}
    \toprule
    Metric & Value \\
    \midrule
    Total Series & 95 \\
    Successful Fits & 95 \\
    \% $\alpha_1$ Significant ($p<0.05$) & 77.89\% \\
    \% $\beta_1$ Significant ($p<0.05$) & 92.63\% \\
    \% Wald Test Rejects H0 ($\alpha_1+\beta_1=0$) & 92.63\% \\
    \% Sum $\alpha_1+\beta_1 > 0.9$ & 64.21\% \\
    \% Sum $\alpha_1+\beta_1 > 0.95$ & 31.58\% \\
    \% Wald Rejects H0 AND Sum $> 0.9$ & 64.21\% \\
    \bottomrule
  \end{tabular}
\end{table}

The GARCH analysis presented in Table~\ref{tab:empirical_garch_params} reveals substantial remaining GARCH effects, pointing to unmodeled idiosyncratic volatility that the initial DFSV-BIF model failed to capture. The estimated GARCH parameters ($\alpha_1$ and $\beta_1$) are statistically significant for a large majority of the residual series. Crucially, the Wald test rejected the null hypothesis of no GARCH effects ($H_0: \alpha_1+\beta_1=0$) for over 92\% of the series. Furthermore, 64\% of the series exhibited high persistence in these residual GARCH dynamics, with $\alpha_1+\beta_1 > 0.9$. This evidence strongly indicates the presence of significant and persistent idiosyncratic volatility dynamics within the DFSV-BIF residuals that were not accounted for by the factor stochastic volatility structure alone.

To confirm that these GARCH effects explain the poor initial diagnostic results, I re-ran the Ljung-Box and ARCH-LM tests on the residuals obtained *after* filtering each original DFSV-BIF residual series through its estimated GARCH(1,1) model. Table~\ref{tab:empirical_test_comparison} compares the diagnostic test pass rates before and after this GARCH correction.

\begin{table}[htbp]
  \centering
  \caption{Diagnostic Test Pass Rate Comparison: Before vs. After GARCH Filtering. Comparison of diagnostic test pass rates (percentage of 95 series with $p > 0.05$) for DFSV-BIF residuals before ('Initial') and after applying GARCH(1,1) filtering ('Post-GARCH').}
  \label{tab:empirical_test_comparison}
  \begin{tabular}{lcc}
    \toprule
    Test & Initial Pass Rate & Post-GARCH Pass Rate \\
    \midrule
    Ljung-Box Sq (Lag 5) & 7.37\% & 93.68\% \\
    Ljung-Box Sq (Lag 10) & 9.47\% & 92.63\% \\
    Ljung-Box Sq (Lag 15) & 10.53\% & 93.68\% \\
    Ljung-Box Sq (Lag 20) & 12.63\% & 94.74\% \\
    ARCH-LM (Lag 5) & 9.47\% & 92.63\% \\
    ARCH-LM (Lag 10) & 11.58\% & 94.74\% \\
    Jarque-Bera & 2.11\% & 9.47\% \\
    \bottomrule
  \end{tabular}
\end{table}

Table~\ref{tab:empirical_test_comparison} clearly demonstrates the efficacy of the GARCH(1,1) correction. The pass rates for both the Ljung-Box test on squared residuals and the ARCH-LM test increased dramatically, from approximately 10\% initially to over 92\% post-correction. This confirms that standard univariate GARCH models effectively account for the conditional heteroskedasticity present in the original DFSV-BIF residuals. The Jarque-Bera pass rate improved only marginally (to 9.47\%), reinforcing the finding that residual non-normality persists even after accounting for volatility dynamics.

These findings effectively resolve the diagnostic puzzle framed in Section~\ref{sec:empirical:framing_puzzle}. The GARCH parameter estimation confirms the presence of significant idiosyncratic volatility dynamics missed by the DFSV-BIF model, while the post-GARCH diagnostic tests demonstrate that these missed dynamics are largely consistent with standard GARCH(1,1) effects. This supports the conclusion that the primary limitation of the DFSV specification employed here lies in its assumption of constant idiosyncratic variances ($\mSigma_{\epsilon}$), which prevents it from capturing the full individual asset volatility. The analysis validates the model's ability to capture common factor dynamics (co-movement) but strongly indicates the need to incorporate time-varying idiosyncratic volatility for a more complete description of asset return dynamics.
\section{Estimation Method Comparison: DFSV-BIF vs. DFSV-PF}
\label{sec:empirical:bif_vs_pf}

This section directly compares the performance of the Bellman Information Filter (BIF) and the Particle Filter (PF) as estimation methods for the same DFSV model specification. The comparison focuses on differences in the optimized statistical fit objective, the quality of latent state estimates, parameter estimates, residual diagnostic performance, and computational requirements.

\subsection{Statistical Fit Objective}
\label{sec:empirical:fit_objective}
Table~\ref{tab:empirical_overall_fit} presents the overall fit metrics achieved by each estimation method. DFSV-BIF attained a final pseudo-log-likelihood value of 222,084.10, substantially higher than the approximate marginal log-likelihood of -17,969.62 obtained by DFSV-PF. However, these values stem from optimizing fundamentally different objective functions and are therefore not directly comparable for model selection purposes (e.g., using AIC or BIC). The large difference observed might reflect inherent differences between the pseudo-likelihood and the true marginal likelihood, or it could indicate challenges faced by the PF optimization procedure in this high-dimensional application.
\begin{table}[htbp]
  \centering
  \caption{Overall Model Fit Comparison (Full Sample, T=738). Log-likelihood for DFSV-BIF is the pseudo-log-likelihood, and for DFSV-PF it is the approximate marginal log-likelihood, both evaluated at the estimated parameters. AIC and BIC penalize for the number of parameters ($p$).}
  \label{tab:empirical_overall_fit}
  \begin{tabular}{lcccc}
    \toprule
    Model       & (Pseudo) Log-Likelihood & Num Params ($p$) & AIC      & BIC      \\
    \midrule
    DFSV-BIF    & 222,084.10              & 610              & -442,948.21 & -440,139.80 \\
    DFSV-PF     & -17,969.62              & 610              & 37,159.23   & 39,967.64   \\
    DFM         & -38,862.75              & 580              & 78,885.50   & 81,555.79   \\
    DCC-GARCH   & 162,516.36              & 288              & -324,456.72 & -323,130.79 \\
    \bottomrule
  \end{tabular}
\end{table}
\subsection{Latent States}
\label{sec:empirical:latent_states_comparison}
Comparing the estimated latent states reveals striking differences in quality between the two methods. As shown previously (Figure~\ref{fig:empirical_factors_bif}), BIF produced smooth and economically interpretable paths for both factors and their volatilities, clearly identifying periods of market stress. In stark contrast, the PF estimates appear erratic and jagged (Figure~\ref{fig:empirical_factors_pf}), offering little clear economic insight. The difference in estimated average conditional correlation starkly highlights this disparity: BIF shows the expected pattern of correlation increasing during crises, whereas PF produced near-zero and flat correlations throughout the sample (Figure~\ref{fig:empirical_avg_corr}). This suggests that the PF, with the chosen number of particles ($P=10,000$), struggled significantly with state estimation in this high-dimensional ($N=95$) setting.
\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{empirical/insample/pf/figures/filtered_states.png}
  \caption{Estimated Latent Factors and Log-Volatilities from DFSV-PF Model (July 1963–Dec 2023). The figure shows smoothed estimates of the $K=5$ latent factors $\hat{\vf}_{t|T}$ (top panel) and their corresponding log-volatilities $\hat{\vh}_{t|T}$ (bottom panel). Individual factor and log-volatility plots are provided in Appendix~\ref{app:pf_factors} and Appendix~\ref{app:pf_logvols}.}
  \label{fig:empirical_factors_pf}
\end{figure}

\subsection{Parameter Estimates}
\label{sec:empirical:parameter_comparison}
Table~\ref{tab:empirical_key_params_comparison} compares key parameter estimates obtained via BIF and PF. The PF estimated considerably higher persistence in the latent factors (max eigenvalue of $\hat{\mPhi}_f$ = 0.5974 vs. 0.3324 for BIF) and near-unit root behavior in the log-volatility process (max eigenvalue of $\hat{\mPhi}_h$ = 0.9997 vs. 0.9857 for BIF). Additionally, the PF estimated higher volatility of volatilities (mean diagonal of $\hat{\mQ}_h$ = 0.0400 vs. 0.0292 for BIF), consistent with the more erratic log-volatility paths observed in Figure~\ref{fig:empirical_factors_pf}. Despite these parameter differences, both methods yielded models that performed similarly poorly on residual diagnostics, as discussed next.

\begin{table}
  \centering
  \caption{Comparison of Key Parameter Estimates (DFSV-BIF vs DFSV-PF). Eigenvalues indicate stationarity. $\hat{\vmu}$ represents the unconditional mean of log-volatilities. $\hat{\mSigma}_\epsilon$ captures idiosyncratic variances, and $\hat{\mQ}_h$ captures the volatility of volatilities.}
  \label{tab:empirical_key_params_comparison}
  \begin{tabular}{lcc}
    \toprule
    Parameter Description & BIF Value & PF Value \\
    \midrule
    Max Eigenvalue($\hat{\mPhi}_f$) & 0.3324 & 0.5974 \\
    Max Eigenvalue($\hat{\mPhi}_h$) & 0.9857 & 0.9997 \\
    Mean($\hat{\vmu}$) & -6.0216 & -5.9995 \\
    Range(diag($\hat{\mSigma}_\epsilon$)) & 0.0002–0.0025 & 0.0003–0.0012 \\
    Mean(diag($\hat{\mSigma}_\epsilon$)) & 0.0006 & 0.0006 \\
    Mean(diag($\hat{\mQ}_h$)) & 0.0292 & 0.0400 \\
    \bottomrule
  \end{tabular}
\end{table}
\subsection{Residual Diagnostics}
\label{sec:empirical:residual_comparison}
As established in Section~\ref{sec:empirical:residual_puzzle}, both DFSV specifications, whether estimated via BIF or PF, failed standard residual diagnostics related to conditional heteroskedasticity (Table~\ref{tab:empirical_resid_pass_rates}). Both methods yielded pass rates generally below 20\% for Ljung-Box (squared) and ARCH-LM tests, performing significantly worse than the benchmark DCC-GARCH model. This indicates that neither estimation approach, when applied to this specific DFSV model structure with constant idiosyncratic variance, adequately addresses the conditional heteroskedasticity present in the individual portfolio returns.

\subsection{Computation \& Stability}
\label{sec:empirical:computation_comparison}
The computational results I observed in this empirical application presented some nuances compared to the simulation study (Chapter~\ref{ch:simulation}). Notably, the BIF estimation required approximately 175 minutes, substantially longer than the PF's 29 minutes (Table~\ref{tab:empirical_estimation_summary}). This finding contrasts with simulation results suggesting potential BIF advantages in higher dimensions, indicating that the complexity of evaluating the BIF's pseudo-likelihood and its gradient scales significantly with the number of assets $N$ in practice. Consequently, each BIF iteration proved considerably more costly in this large-scale ($N=95$) empirical setting, even though fewer iterations were required compared to the PF (289 vs. 466). Furthermore, I observed empirical instability in the PF, particularly in generating plausible latent state dynamics (Section~\ref{sec:empirical:latent_states_comparison}); this challenge was not fully reflected in the median simulation results and highlights difficulties in applying the standard Bootstrap PF reliably in this context. Despite the BIF's higher computational cost in this application, its demonstrably superior quality of latent state estimates might justify the additional time when state interpretability is a primary objective.


\section{Chapter Discussion}
\label{sec:empirical:conclusion}

This chapter's empirical application of the DFSV model using the Bellman Information Filter revealed a central puzzle. On the one hand, the DFSV-BIF model produced plausible aggregate dynamics (Section~\ref{sec:empirical:comparative_dynamics}), capturing overall market variance and correlation patterns similarly to a benchmark DCC-GARCH model. It also yielded interpretable parameters and latent states (Section~\ref{sec:empirical:internal_plausibility}) that align well with economic intuition and established stylized facts about financial markets, such as volatility clustering and time-varying factor importance.

On the other hand, the model failed standard residual diagnostics designed to detect remaining conditional heteroskedasticity at the individual asset level (Section~\ref{sec:empirical:residual_puzzle}). Its performance on these tests was significantly worse than simpler benchmarks like DCC-GARCH and, in some cases, no better than a constant-volatility DFM (Table~\ref{tab:empirical_resid_pass_rates}). Further diagnostic analysis involving fitting GARCH models to the DFSV-BIF residuals (Section~\ref{sec:garch_analysis}) strongly suggests that this failure stems primarily from the model's restrictive assumption of constant idiosyncratic variance ($\mSigma_{\epsilon}$). This assumption effectively overlooks significant asset-specific GARCH effects that remain present in the residuals after accounting for the common factor dynamics.

This finding highlights a critical limitation of the current DFSV specification for capturing the nuances of individual asset dynamics, despite its apparent success at modeling aggregate market behavior. While the BIF estimation proved superior to the PF in generating interpretable latent states in this high-dimensional setting (Section~\ref{sec:empirical:bif_vs_pf}), neither method could overcome the specification issue related to idiosyncratic volatility. The results underscore the potential need for extensions to the DFSV framework, such as incorporating stochastic volatility in the idiosyncratic components, to achieve a more comprehensive representation of financial asset return dynamics.