#!/bin/bash
# Build script for CFA Quant Awards submission

echo "Building CFA Quant Awards submission..."

# Clean previous build files
echo "Cleaning previous build files..."
rm -f *.aux *.bbl *.bcf *.blg *.log *.out *.run.xml *.synctex.gz

# Build process
echo "Running pdflatex (first pass)..."
pdflatex main.tex

echo "Running biber..."
biber main

echo "Running pdflatex (second pass)..."
pdflatex main.tex

echo "Running pdflatex (final pass)..."
pdflatex main.tex

# Check if PDF was created
if [ -f "main.pdf" ]; then
    echo "✅ Build successful! PDF created: main.pdf"
    echo "📄 Pages: $(pdfinfo main.pdf 2>/dev/null | grep Pages | awk '{print $2}' || echo 'Unknown')"
    echo "📁 File size: $(du -h main.pdf | cut -f1)"
else
    echo "❌ Build failed! PDF not created."
    exit 1
fi
