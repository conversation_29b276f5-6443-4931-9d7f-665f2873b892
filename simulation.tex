\chapter{Simulation Studies: Evaluating the Bellman Information Filter}\label{ch:simulation}

This chapter evaluates the Bellman Information Filter (BIF) through two distinct simulation studies. The first study focuses on computational performance and scalability, comparing the BIF with the Particle Filter (PF) across different model dimensions. The second study assesses the accuracy of state-estimation and parameter-recovery, providing insights into the filter's ability to recover the true model parameters and latent states. The chapter concludes with a synthesis of findings, discussing the trade-offs between computational efficiency and estimation accuracy.

\section{Simulation Study 1: Computational Performance and Scalability}\label{sec:sim1}

This section compares the Bellman Information Filter's (BIF) tractability against a standard Particle Filter (PF) benchmark as model dimensionality grows. I generated synthetic data from the DFSV model using stable parameters detailed in Appendix~\ref{app:sim1_params} (Table~\ref{tab:dgp_parameters_study1_appendix}). The simulation covers a time-series length of $T=1500$ and dimensions $N \in \{5, \dots, 150\}$, $K \in \{2, \dots, 15\}$ ($K < N$), running 100 replications per configuration. The PF uses varying particle counts ($P \in \{1{,}000, \dots, 20{,}000\}$). I performed computations using an Intel Ultra 7 155H processor with 16GB RAM and evaluated the median computation time and median Root Mean Squared Error (RMSE) for the latent factors ($\vf_t$) and latent factor log-volatilities ($\vh_t$).

\subsection{Summary Results}\label{sec:sim1:summary_results}

Table~\ref{tab:sim1:summary_metrics} details the median runtime and state-estimation accuracy for representative small (N=50, K=5) and large (N=150, K=15) models, comparing the BIF to the PF-20k (20,000 particles). The BIF's speed advantage in the smaller dimension indicates that PF particle overhead can dominate the BIF's complexity in $K$. However, the PF-20k runs faster for the larger dimension, revealing the BIF's cubic scaling in $K$. Despite this scaling difference, the BIF maintains competitive latent factor accuracy and achieves better latent factor log-volatility accuracy than the PF-20k in the large model. The BIF's higher latent factor RMSE but lower latent factor log-volatility RMSE in the smaller dimension reflects different approximations: the BIF's Gaussian assumption may capture linear factor dynamics less accurately than PF particles in this case, while its structured approach proves more effective for the non-linear volatility states.

\begin{table}[htb]
    \centering
    \caption{Summary of Median Runtime and State-Estimation RMSE.}
    \label{tab:sim1:summary_metrics}
    \begin{tabular}{lllrrr}
        \toprule
        Filter & N & K & Median Time (s) & Median RMSE Latent Factors ($\vf_t$) & Median RMSE Latent Log-Vols ($\vh_t$) \\
        \midrule
        BIF     & 50 & 5 & 4.320  & 0.450 & 0.805 \\
        PF-20k  & 50 & 5 & 5.350  & 0.198 & 0.913 \\
        \midrule
        BIF     & 150 & 15 & 31.300 & 1.137 & 1.360 \\
        PF-20k  & 150 & 15 & 11.820 & 1.114 & 1.936 \\
        \bottomrule
    \end{tabular}
    \par
    \footnotesize PF-20k refers to the Particle Filter with 20,000 particles. Median RMSE and time values are extracted from detailed simulation results across 100 replications.
\end{table}

\subsection{Timing Results vs. Dimensionality (N, K)}\label{sec:sim1:timing}

Figure~\ref{fig:sim1:time_vs_k} plots how median computation time scales with the number of factors $K$ (for a fixed N=50). The BIF's runtime increases more steeply than the PF variants, reflecting its higher theoretical complexity in $K$. PF runtimes scale more gently with $K$ but increase substantially with the particle count. Figure~\ref{fig:sim1:time_vs_n} plots scaling with the number of series $N$ (for a fixed K=5); both filters scale approximately linearly with $N$. However, the BIF shows a steeper slope, particularly for $N > 100$. These plots confirm the computational trade-offs between the filters.

\begin{figure}[htbp]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_time_vs_K_N50_color.png}
        \caption{Runtime scaling with factors $K$ (N=50)}
        \label{fig:sim1:time_vs_k}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_time_vs_N_K5_color.png}
        \caption{Runtime scaling with series $N$ (K=5)}
        \label{fig:sim1:time_vs_n}
    \end{subfigure}
    \caption{Computational scaling comparison. Left: BIF runtime scales faster with $K$ than PF runtimes, which grow linearly with particle count. Right: Both filters scale approximately linearly with $N$, with BIF showing steeper growth at higher dimensions.}
\end{figure}

\subsection{Analysis of Scalability}\label{sec:sim1:scalability}

Scalability defines how computational requirements grow with problem size. Figure~\ref{fig:sim1:time_vs_k} shows the BIF's runtime growing roughly cubically with $K$, consistent with its $O(K^3)$ complexity. Conversely, Figure~\ref{fig:sim1:time_vs_n} confirms a near-linear dependence on $N$. In contrast, PF runtime scales approximately linearly with both the particle count $P$ and the series count $N$.

Figure~\ref{fig:sim1:heatmaps_bif} plots the BIF median runtime and log-volatility RMSE across the $N \times K$ grid. The heatmaps reveal the BIF's runtime sensitivity to $K$ (Figure~\ref{fig:sim1:heatmap_time_bif}, where darker cells indicate longer runtimes) and its relatively stable log-volatility RMSE across dimensions (Figure~\ref{fig:sim1:heatmap_rmse_h_bif}, where darker cells mean higher error). The BIF's deterministic nature offers advantages for reliability. Its primary computational bottleneck involves matrix operations related to the $O(K^2)$ state covariance $\hat{\mP}_t$. In contrast, the PF bottleneck typically lies in resampling and likelihood evaluation, scaling with $P$ and $N$.

\begin{figure}[htbp]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/heatmap_time_bif.png}
        \caption{BIF median runtime increases with N and K, with steeper growth along K axis}
        \label{fig:sim1:heatmap_time_bif}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/heatmap_rmse_h_bif.png}
        \caption{BIF median log-volatility RMSE remains stable across N and K combinations}
        \label{fig:sim1:heatmap_rmse_h_bif}
    \end{subfigure}
    \caption{Heatmap visualization of BIF performance metrics. Darker cells in (a) indicate longer runtimes (seconds), consistent with theoretical complexity. Darker cells in (b) indicate higher RMSE (worse accuracy) for log-volatility estimation.}
    \label{fig:sim1:heatmaps_bif}
\end{figure}

\subsection{State Estimation Accuracy}\label{sec:sim1:state_accuracy}

I assess the accuracy of recovering the true latent factors ($\vf_t$) and latent factor log-volatilities ($\vh_t$) using median RMSE and correlation as metrics.

\subsubsection{Latent Factor State Estimation}

Figure~\ref{fig:sim1:factor_accuracy} plots the median RMSE and correlation for latent factor estimation as $K$ increases (N=50). PF variants with sufficient particles generally achieve lower latent factor RMSE than the BIF, particularly for smaller $K$. However, this advantage diminishes as $K$ increases (Figure~\ref{fig:sim1:rmse_f}). A similar pattern holds for correlation (Figure~\ref{fig:sim1:corr_f}). While the PF can yield marginally better latent factor estimates, the BIF's performance becomes increasingly comparable as model complexity grows.

\begin{figure}[htbp]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_rmse_f_vs_K_N50_color.png}
        \caption{Latent Factor RMSE vs. K (N=50)}
        \label{fig:sim1:rmse_f}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_corr_f_vs_K_N50_color.png}
        \caption{Latent Factor correlation vs. K (N=50)}
        \label{fig:sim1:corr_f}
    \end{subfigure}
    \caption{Latent factor state-estimation accuracy. (a) PF variants show lower median latent factor RMSE than BIF at low K, but the gap narrows as K increases. (b) PF variants achieve slightly higher median latent factor correlation than BIF across K, though the difference is marginal for K=15.}
    \label{fig:sim1:factor_accuracy}
\end{figure}

\subsubsection{Latent Factor Log-Volatility State Estimation}

Figure~\ref{fig:sim1:volatility_accuracy} plots the corresponding metrics for latent factor log-volatility estimation. The BIF consistently achieves lower latent factor log-volatility RMSE compared to the PF variants, and this advantage widens as $K$ increases. At $K=15$, the BIF reduces latent factor log-volatility RMSE by more than 30\% compared to the best PF variant (Figure~\ref{fig:sim1:rmse_h}). The BIF also maintains higher correlations with the true latent factor log-volatilities, especially for larger $K$ (Figure~\ref{fig:sim1:corr_h}). These findings confirm the BIF's superior ability to estimate latent factor log-volatility states, a key advantage in financial applications. The combined results reveal a trade-off: the PF (with high $P$) offers slightly better latent factor estimation in lower dimensions, while the BIF provides superior latent factor log-volatility estimation, especially in higher dimensions.

\begin{figure}[htbp]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_rmse_h_vs_K_N50_color.png}
        \caption{Latent Factor Log-volatility RMSE vs. K (N=50)}
        \label{fig:sim1:rmse_h}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation1_analysis/figures/median_corr_h_vs_K_N50_color.png}
        \caption{Latent Factor Log-volatility correlation vs. K (N=50)}
        \label{fig:sim1:corr_h}
    \end{subfigure}
    \caption{Latent factor log-volatility state-estimation accuracy. (a) BIF consistently achieves lower median latent factor log-volatility RMSE compared to PF variants, with the advantage widening as K increases. (b) BIF maintains higher median latent factor log-volatility correlation than PF variants, particularly for larger K.}
    \label{fig:sim1:volatility_accuracy}
\end{figure}

\subsection{Discussion}\label{sec:sim1:discussion}

Simulation Study 1 confirms trade-offs between the BIF and PF concerning computational cost, accuracy, and robustness. The BIF confirms superior latent factor log-volatility estimation and offers deterministic runtime with predictable scaling (cubic in $K$, linear in $N$), making it attractive for reliability. PF runtime scales linearly with $N$ and particle count $P$. However, achieving sufficient accuracy often requires a large $P$, potentially negating its scaling advantage in some regimes (Table~\ref{tab:sim1:summary_metrics}, N=50, K=5). A significant PF limitation is its susceptibility to instability, particularly with insufficient particles or in high dimensions \cite{rebeschini_can_2015}. This often requires large $P$ and assessment via median metrics (see Appendix~\ref{app:stability_tables} for stability details). In contrast, the BIF's deterministic nature avoids the risk of filter collapse. While its cubic scaling in $K$ limits applicability for very large $K$, it provides a compelling balance of efficiency and reliable state estimation (especially for latent factor log-volatilities) for the dimensions I study (up to N=150, K=15). The choice between filters depends on model dimensions, accuracy priorities, and tolerance for PF instability.

\section{Simulation Study 2: State Estimation \& Parameter Recovery}\label{sec:sim2}

This study assesses the ability of BIF and PF estimators to recover the true DFSV parameters ($\mTheta$) and latent states (latent factors $\vf_t$ and latent factor log-volatilities $\vh_t$) via Maximum Likelihood Estimation (MLE) on simulated data when the parameters are unknown. The simulation design (detailed in Appendix~\ref{app:sim2_params}, Table~\ref{tab:dgp_parameters_study2_appendix}) covers various time-series lengths and dimensions across all $N, K, T$, using 5 replications per configuration. I assess parameter/state accuracy, bias, and computational scaling. My results suggest that the BIF generally achieves superior parameter-recovery and state-estimation (particularly for latent factor log-volatilities) with a favorable bias-variance trade-off.

\subsection{Parameter accuracy}\label{sec:sim2:param}

Table~\ref{tab:sim2:param_median_rmse_bias} details the median RMSE and bias for key parameter blocks ($\mLambda, \mPhi_f, \mPhi_h, \vmu, \mSigma_{\epsilon}, \mQ_h$) across the simulation grid, comparing the BIF against the PF-10k. The BIF confirms superior recovery for most blocks. Notably, the BIF achieves over 20 times lower median RMSE for factor loadings ($\mLambda$) than PF-10k. The BIF also achieves substantially lower median RMSE for the volatility transition matrix ($\mPhi_h$) and idiosyncratic variances ($\mSigma_{\epsilon}$), and slightly lower error for the factor transition matrix ($\mPhi_f$). Estimating the log-volatility mean ($\vmu$) remains challenging for both methods, showing comparable RMSE. The PF-10k confirms lower median RMSE only for the volatility innovation covariance ($\mQ_h$), although the BIF exhibits lower median bias for this parameter.

\begin{table}[htb]
    \centering
    \caption{Parameter-recovery Median RMSE and Median Bias. Aggregated over the full design grid of $(N,K,T)$ configurations.}
    \label{tab:sim2:param_median_rmse_bias}
    \begin{tabular}{lrrrr}
    \toprule
    & \multicolumn{2}{c}{BIF} & \multicolumn{2}{c}{PF-10k} \\
    \cmidrule(lr){2-3} \cmidrule(lr){4-5}
    Parameter & Median RMSE & Median Bias & Median RMSE & Median Bias \\
    \midrule
    Factor Loadings ($\mLambda$) & 0.02 & 0.00 & 0.41 & -0.17 \\
    Factor Transition ($\mPhi_f$) & 0.05 & 0.03 & 0.07 & -0.03 \\
    Volatility Transition ($\mPhi_h$) & 0.04 & 0.01 & 0.12 & 0.00 \\
    Latent Log-Volatility Mean ($\vmu$) & 1.08 & 0.89 & 1.01 & 1.01 \\
    Idiosyncratic Variance ($\mSigma_{\epsilon}$) & 0.01 & 0.00 & 0.06 & 0.05 \\
    Volatility Innovation Covariance ($\mQ_h$) & 0.21 & -0.10 & 0.04 & -0.01 \\
    \bottomrule
    \end{tabular}
\end{table}

Figure~\ref{fig:sim2:param_error_scaling} plots how median RMSE scales with dimensions. The top panel reveals the BIF's accuracy for $\mLambda$; it maintains near-zero RMSE irrespective of $N, K, T$, while the PF variants exhibit substantially higher RMSE. The bottom panel shows the BIF's superior performance for $\mPhi_h$, achieving lower RMSE than all PF variants across dimensions. Accuracy generally improves with $T$ for all methods, but the BIF's advantage is clear, especially compared to the PF-10k which struggles with $\mPhi_h$ as $N$ and $K$ increase.

\begin{figure}[htb]
    \centering
    \begin{subfigure}[b]{0.85\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_lambda_r_rmse.pdf}
        \caption{Factor loadings ($\mLambda$) RMSE}
        \label{fig:sim2:lambda_rmse}
    \end{subfigure}

    \vspace{0.3cm}
    \begin{subfigure}[b]{0.85\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_parameter_estimation_Phi_h_rmse.pdf}
        \caption{Latent Factor Log-Volatility transition matrix ($\mPhi_h$) RMSE}
        \label{fig:sim2:phi_h_rmse}
    \end{subfigure}
    \caption{Parameter estimation error (median RMSE) scaling with model dimensions ($N, K, T$). BIF consistently achieves lower RMSE for both parameter blocks compared to PF variants, with the advantage being particularly pronounced for $\mLambda$. Accuracy generally improves with increasing time-series length $T$ for all methods. See Appendix~\ref{app:sim2:param_scaling} for additional parameters.}
    \label{fig:sim2:param_error_scaling}
\end{figure}

\subsection{Latent State Accuracy (Post-Estimation)}\label{sec:sim2:state}

Figure~\ref{fig:sim2:state_rmse} plots the median RMSE for filtered latent states using estimated parameters, scaling with dimensions $N, K, T$. For latent factor states ($\vf_t$), PF variants (especially PF-10k) are competitive and often outperform the BIF in smaller configurations (lower $N, K$). As dimensions increase, this advantage diminishes, and the BIF's performance becomes comparable or occasionally superior. The BIF's latent factor accuracy remains relatively stable with $N$, degrades slightly with $K$, and improves slightly with $T$. In contrast, Figure~\ref{fig:sim2:vol_rmse} confirms the BIF's significant advantage for latent factor log-volatility states ($\vh_t$). The BIF consistently achieves the lowest median latent factor log-volatility RMSE across almost all dimensions. Its performance remains remarkably stable with increasing $N$ and $T$, showing only a minor error increase with $K$. All PF variants exhibit substantially higher latent factor log-volatility RMSE. This underscores the BIF's robustness in latent factor log-volatility estimation, even with parameter uncertainty, while acknowledging the PF's strength in latent factor estimation for smaller models.

\begin{figure}[htb]
    \centering
    \begin{subfigure}[b]{0.85\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_state_estimation_factor_rmse_mean.png}
        \caption{Latent Factor states ($\hat{\vf}_{t|t}$) RMSE}
        \label{fig:sim2:factor_rmse}
    \end{subfigure}

    \vspace{0.3cm}
    \begin{subfigure}[b]{0.85\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/error_scaling_accuracy_state_estimation_volatility_rmse_mean.png}
        \caption{Latent Factor Log-volatility states ($\hat{\vh}_{t|t}$) RMSE}
        \label{fig:sim2:vol_rmse}
    \end{subfigure}
    \caption{Median RMSE for filtered latent states using estimated parameters, plotted against model dimensions $N$, $K$, and $T$. (a) BIF latent factor state accuracy is competitive with PF-10000 and generally better than lower-particle PFs. (b) BIF consistently achieves the lowest latent factor log-volatility RMSE compared to all PF variants across dimensions.}
    \label{fig:sim2:state_rmse}
\end{figure}

\subsection{Bias \& identification difficulty}\label{sec:sim2:bias}

Figure~\ref{fig:sim2:bias_scatter} plots bias (horizontal axis) against RMSE (vertical axis) for the latent factor log-volatility transition parameters ($\mPhi_h$) across configurations. The BIF estimates (pink circles) cluster tightly near zero bias with low RMSE, indicating low systematic bias and high precision (low variance). In contrast, the PF estimates (teal, purple, olive markers) exhibit significantly wider dispersion both horizontally and vertically. While centered around zero bias on average, their high variance means individual estimates can suffer substantial bias and large RMSE. Increasing time-series length $T$ (circles to crosses to squares) improves performance for all filters, but the BIF maintains lower variance. This figure, focusing on the crucial $\mPhi_h$ parameter block, confirms the BIF's superior bias-variance trade-off compared to PFs. Similar analyses for other parameters are deferred to Appendix~\ref{app:sim2_bias_appendix}.

\begin{figure}[htb]
    \centering
    \includegraphics[width=0.9\textwidth]{simulation2_analysis/scatter_Phi_h_comparison.pdf}
    \caption{Bias versus RMSE for $\mPhi_h$ parameter estimates across different $(N,K)$ pairs. Marker shape indicates time-series length $T$. BIF estimates (pink) cluster tightly near zero bias with low RMSE, while PF variants show higher dispersion.}
    \label{fig:sim2:bias_scatter}
\end{figure}

\subsection{Computational cost and Accuracy Trade-offs}\label{sec:sim2:cost}

Computationally, Table~\ref{tab:sim2:cost_accuracy_comparison} details cost alongside state-estimation accuracy. The PFs exhibit a clear speed-accuracy trade-off: raising the particle count from 1,000 to 10,000 increases the median runtime tenfold (from 56s to 606s) while significantly improving factor RMSE (from 4.36 to 1.54). The BIF shows a median runtime of 692s, achieving competitive factor RMSE (2.51) and the lowest volatility RMSE (1.56). Although the aggregated runtime is similar to the PF-10k here, the BIF's relative efficiency may improve in higher dimensions where PF performance degrades more rapidly. Figure~\ref{fig:sim2:time_scaling} plots time scaling: the BIF confirms approximately linear growth with $N$ and $T$, but cubic complexity with $K$, consistent with Section~\ref{sec:sim1:scalability}. PF variants scale more gradually with $K$ but depend heavily on the particle count. This implies the BIF may be preferable for moderate $K$ and large $N$, while PFs might be more efficient for large $K$ relative to $N$, assuming stability can be maintained.

\begin{table}[htb]
    \centering
    \caption{Computational cost and accuracy comparison (median/mean over configurations). Aggregated across all (N, K, T) settings.}
    \label{tab:sim2:cost_accuracy_comparison}
    \begin{tabular}{lrrrrr}
    \toprule
    & \multicolumn{3}{c}{Computational Cost} & \multicolumn{2}{c}{Accuracy (Avg RMSE)} \\
    \cmidrule(lr){2-4} \cmidrule(lr){5-6}
    Filter & Med Time (s) & Med Time/Iter (s) & Med Std Dev Time (s) & Latent Factor & Latent Log-Volatility \\
    \midrule
    BIF & 692.1 & 1.89 & 176.0 & 2.505 & 1.561 \\
    PF-1k & 56.4 & 0.22 & 17.2 & 4.358 & 2.306 \\
    PF-5k & 240.5 & 0.86 & 109.0 & 3.952 & 2.456 \\
    PF-10k & 605.6 & 2.20 & 230.4 & 1.535 & 2.048 \\
    \bottomrule
    \end{tabular}
\end{table}

\begin{figure}[htb]
    \centering
    \begin{subfigure}[b]{0.32\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/time_scaling_N.pdf}
        \caption{Scaling with number of assets $N$}
        \label{fig:sim2:time_scaling_N}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.32\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/time_scaling_K.pdf}
        \caption{Scaling with number of factors $K$}
        \label{fig:sim2:time_scaling_K}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.32\textwidth}
        \centering
        \includegraphics[width=\textwidth]{simulation2_analysis/time_scaling_T.pdf}
        \caption{Scaling with time-series length $T$}
        \label{fig:sim2:time_scaling_T}
    \end{subfigure}
    \caption{Computational time scaling with model dimensions. BIF shows approximately linear growth with $N$ and $T$, but cubic complexity with $K$. PF variants exhibit more gradual scaling with $K$ but are heavily influenced by particle count.}
    \label{fig:sim2:time_scaling}
\end{figure}

\subsection{Synthesis}\label{sec:sim2:synthesis}

Simulation Study 2 confirms the BIF's strong capabilities in joint parameter and state estimation for DFSV models. The BIF consistently provides superior recovery for most parameters (especially $\mLambda$ and $\mPhi_h$), showing significantly lower RMSE and variance than PFs (Section~\ref{sec:sim2:param}, Table~\ref{tab:sim2:param_median_rmse_bias}, Figure~\ref{fig:sim2:bias_scatter}). While PFs can be competitive for estimating latent factor states in lower dimensions, the BIF provides substantially more accurate latent factor log-volatility states across all configurations (Section~\ref{sec:sim2:state}, Figure~\ref{fig:sim2:state_rmse}). Computationally, the BIF's runtime is comparable to the PF-10k in these dimensions, although its cubic scaling in $K$ contrasts with the PF's dependence on particle count (Section~\ref{sec:sim2:cost}).

This study validates the BIF as a robust and accurate method for estimating DFSV models. Its key advantages are precise parameter-recovery (loadings, volatility persistence) and superior latent factor log-volatility estimation. It offers a favorable bias-variance profile (Section~\ref{sec:sim2:bias}) and greater reliability compared to PFs, which suffer from higher parameter variance and less accurate volatility tracking. The BIF's deterministic nature and reliable performance render it highly suitable for financial applications where volatility dynamics are crucial (e.g., recovering latent factor log-volatility states with 40\% lower RMSE than the best PF (N=15, K=5)). The main trade-off involves computational scaling with $K$, which becomes relevant primarily when $K$ exceeds 15–20 factors.

\section{Overall Discussion: Synthesis of Simulation Findings}\label{sec:sim:overall_discussion}

These simulation studies compare the Bellman Information Filter (BIF) against Particle Filter (PF) benchmarks for estimating DFSV models, directly addressing Research Question 2 (Section~\ref{sec:intro_rq_contrib}) regarding comparative performance across dimensions. Study 1 (Section~\ref{sec:sim1}) focused on computational efficiency and state estimation with known parameters, while Study 2 (Section~\ref{sec:sim2}) assessed joint parameter and state recovery.

Synthesizing the findings, the BIF's primary strength lies in accurately estimating the latent factor log-volatilities ($\vh_t$) and recovering related parameters ($\mPhi_h$, $\mLambda$) (Figure~\ref{fig:sim1:volatility_accuracy}, Figure~\ref{fig:sim2:state_rmse}, Table~\ref{tab:sim2:param_median_rmse_bias}, Figure~\ref{fig:sim2:bias_scatter}). This accurate estimation of $\vh_t$ is particularly crucial for modeling dynamic co-movement, as these log-volatilities directly govern the time-varying covariance of the factor innovations (Equation~\ref{factor_AR}) and thus the overall market co-movement dynamics. The BIF's ability to reliably recover these states and related parameters suggests greater reliability for inference compared to PFs, which exhibit higher variance in parameter recovery. While well-tuned PFs can achieve competitive latent factor state accuracy in lower dimensions (Figure~\ref{fig:sim1:factor_accuracy}, Figure~\ref{fig:sim2:factor_rmse}), they consistently struggle with latent factor log-volatility estimation.

Regarding runtime, the BIF scales linearly with the number of assets $N$ and time $T$, but cubically with the number of factors $K$ (Figure~\ref{fig:sim1:time_vs_k}, Figure~\ref{fig:sim2:time_scaling}), representing its main bottleneck. PF runtime scales more gently with $K$ but linearly with $N$ and the particle count $P$. Achieving acceptable PF accuracy often requires a large $P$, leading to comparable or even slower runtimes than the BIF in the configurations studied (Table~\ref{tab:sim1:summary_metrics}, Table~\ref{tab:sim2:cost_accuracy_comparison}).

Numerically, the BIF's deterministic nature offers significant robustness against PF instability issues like filter divergence or particle degeneracy (Section~\ref{sec:sim1:discussion}). This represents a key practical advantage.

Answering Research Question 2 directly:
\begin{enumerate}
    \item \textbf{Runtime scaling:} The BIF scales $O(N T K^3)$, while the PF scales roughly $O(N T P)$. The BIF remains competitive for dimensions typical in finance where $N \gg K$.
    \item \textbf{Numerical stability:} The BIF provides superior, deterministic performance without the PF's risk of filter collapse.
    \item \textbf{Latent factor-state accuracy:} The PF achieves slightly better accuracy in low dimensions; the BIF is competitive across the tested configurations.
    \item \textbf{Latent factor log-volatility-state accuracy:} The BIF consistently outperforms PFs across all dimensions examined.
\end{enumerate}

The choice between filters depends on priorities. For accurate modeling of dynamic co-movement (driven by latent factor log-volatilities), reliable parameter inference, and predictable performance, the BIF is a strong candidate. If latent factor accuracy in low dimensions is the sole priority and potential PF instability is manageable, or if $K$ is very large relative to $N$, a PF is considered. Note that the BIF's Gaussian approximation performs well in these simulations but is a limitation when dealing with extreme non-Gaussianities.

Based on this evidence, the BIF proves highly suitable for the empirical applications I present in subsequent chapters. It provides strengths in capturing volatility dynamics and factor structures, computational feasibility, and reliability for typical financial panel data dimensions, aligning well with modeling time-varying covariances and market co-movement. Limitations of this analysis include the reliance on specific Data Generating Processes (DGPs) and parameter values. The range of dimensions explored does not cover all possible scenarios. Future work can extend this analysis to alternative model specifications, larger dimensions, and different distributional assumptions. In the next chapter, I will validate the models on real-world data.