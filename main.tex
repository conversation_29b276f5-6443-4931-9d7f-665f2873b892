% !TeX root = main.tex
% This template can serve as a starting point for your MSc thesis. You are allowed to modify it as long as you adhere to the requirements from the Thesis Manual.

\documentclass[a4paper,11pt]{report}

% FILL OUT THE DETAILS BELOW:
\author{<PERSON><PERSON><PERSON>}
\title{High-Dimensional Dynamic Factor Stochastic Volatility Estimation}
\newcommand{\subtitle}{A Block-Coordinate Bellman Filtering Approach}
\date{30-04-2025}
\newcommand{\thesisnumber}{123456}
\newcommand{\studentnumber}{582172}
\newcommand{\program}{MSc Quantitative Finance}
\newcommand{\supervisor}{<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>}
\newcommand{\secondassesor}{Dr. <PERSON>}

\usepackage[british]{babel} % Use British English
\usepackage[onehalfspacing]{setspace} % Increase line spacing
\usepackage[margin=2.5cm]{geometry} % Modify margins
\usepackage{amsmath,amsfonts,hyperref}
\usepackage{graphicx,booktabs} % Packages for images, tables, and APA citations
\usepackage{csquotes}
\usepackage{bm}
\usepackage{econometrics}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{tabularx}
\usepackage{mwe}
\usepackage{cleveref}
\usepackage{enumitem}
\usepackage{ifthen}
\usepackage{caption} % Explicitly load caption
\usepackage{subcaption} % For subfigure environment
\usepackage{nomencl}
\usepackage{etoolbox}

\usepackage[
    style        = apa,      % APA 7th-edition output
    backend      = biber,    % run ‘biber’ instead of ‘bibtex’
    doi          = true,     % render DOI as https://doi.org/…
    url          = false,    % hide long publisher URLs when DOI present
    eprint       = true,     % show arXiv / SSRN identifiers
    giveninits   = true,     % “J. Doe” instead of “John Doe”
    maxcitenames = 2,        % “Doe & Smith” → “Doe et al.” from 3+
    uniquelist   = false,    % avoid 2020a/2020b when possible
    sorting      = nyt,      % author–year–title order
]{biblatex}

\DeclareLanguageMapping{british}{british-apa}  % correct capitalisation rules

\addbibresource{references.bib}
% \addbibresource{extrarefs.bib}

\AtBeginEnvironment{tabular}{\fontsize{10}{12}\selectfont}
\captionsetup{font=small,labelfont=bf} % Set font size and label font for captions
\captionsetup[sub]{skip=2pt}
\makenomenclature
\renewcommand{\nomname}{List of Abbreviations and Symbols}

% Configure nomenclature prefixes for different categories
\renewcommand{\nomgroup}[1]{%
  \ifthenelse{\equal{#1}{a}}{\item[\textbf{Abbreviations}]}{}%
  \ifthenelse{\equal{#1}{v}}{\item[\textbf{Vectors}]}{}%
  \ifthenelse{\equal{#1}{m}}{\item[\textbf{Matrices}]}{}%
  \ifthenelse{\equal{#1}{s}}{\item[\textbf{Scalars}]}{}%
  \ifthenelse{\equal{#1}{g}}{\item[\textbf{Greek Symbols}]}{}%
  \ifthenelse{\equal{#1}{o}}{\item[\textbf{Operators and Functions}]}{}%
}
\DeclareMathOperator{\Var}{\mathrm{Var}}
\DeclareMathOperator{\Cov}{\mathrm{Cov}}
\DeclareMathOperator{\Diag}{\mathrm{diag}}
% ADD YOUR OWN PACKAGES HERE
% Hint: \usepackage{amsmath,amsfonts,hyperref} imports some frequently used packages
% Diagrams
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows.meta, positioning, calc,fit, backgrounds}


\tikzset{
  startstop/.style = {
    ellipse, draw, align=center,
    minimum width=2cm, minimum height=0.8cm,
    font=\small, fill=gray!20
  },
  param/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=3.5cm, minimum height=0.9cm,
    font=\small, inner sep=2pt, fill=yellow!20
  },
  init/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=4.5cm, minimum height=0.9cm,
    font=\small, inner sep=2pt, fill=orange!20
  },
  loopcontainer/.style = {
    draw, dashed, rounded corners=3pt, fill=gray!5
  },
  updh/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=3.5cm, minimum height=0.8cm,
    font=\small, inner sep=2pt, fill=magenta!30
  },
  updf/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=3.5cm, minimum height=0.8cm,
    font=\small, inner sep=2pt, fill=blue!30
  },
  updr/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=3.5cm, minimum height=0.8cm,
    font=\small, inner sep=2pt, fill=green!30
  },
  output/.style = {
    rectangle, draw, rounded corners=1pt, align=center,
    minimum width=3.5cm, minimum height=0.9cm,
    font=\small, inner sep=2pt, fill=teal!20
  },
  >={Stealth[length=3pt]},
  every edge/.style={draw, very thin}
}
\let\citeA  \textcite   % active, sentence-case author   →  \citeA{key}
\let\CiteA  \Textcite   % active, capitalised Author     →  \CiteA{key}

% Remove publisher for bibliography
\AtEveryBibitem{%
  \ifentrytype{article}{% Is the entry an article?
    \clearfield{publisher}% If yes, clear the publisher field
    \clearfield{location}%  Might as well clear location too for articles
  }{% else (not an article)
    % Do nothing for other types
  }%
}

\begin{document}

% Include nomenclature entries
\input{nomenclature}

\pagenumbering{roman}
\setcounter{tocdepth}{1}
\begin{titlepage}
\makeatletter
\begin{center}
	\textsc{Erasmus University Rotterdam}
	\par \textsc{Erasmus School of Economics}
	\par Master Thesis \program

        \vfill \hrule height .08em \bigskip
        \par\huge\@title\bigskip
        \par\Large\textit{\subtitle}\bigskip % <-- Subtitle added here
        \par\Large\@author\,(\studentnumber)\bigskip
        \hrule height .08em\normalsize

	\vfill
	\includegraphics[width=\textwidth,height=0.15\textheight,keepaspectratio]{eur} % The EUR logo, but this could also be another image
	\vfill

	\begin{tabular}{ll}
		\toprule
		Supervisor: & \supervisor\\
    Second Assessor: & \secondassesor\\
		\midrule
		Date final version: & \@date\\
		\bottomrule
	\end{tabular}

	\vfill
	The content of this thesis is the sole responsibility of the author and does not reflect the views of the supervisor, second assessor, Erasmus School of Economics, or Erasmus University.
\end{center}
\makeatother
\end{titlepage}

\begin{abstract}
  Accurately modeling high-dimensional financial co-movement and its underlying systematic volatility requires both tractable estimation methods and correctly specified models. This thesis evaluates the Bellman Information Filter (BIF) for Dynamic Factor Stochastic Volatility (DFSV) models, used here to model dynamic factor structures and the resulting asset co-movement, adapting the filter via block-coordinate descent for stable, efficient ($\mathcal{O}(K^3+N)$) estimation. While simulations confirm BIF's superior factor log-volatility tracking (+30\% vs a Particle Filter with 10,000 particles) and stability (up to $N=150$ assets and $K=15$ factors), empirical application (95 Fama-French portfolios) reveals a critical insight: despite BIF producing plausible aggregate dynamics and co-movement patterns, residual diagnostics fail, highlighting the model's limitation in capturing full asset-specific volatility due to the standard DFSV assumption of constant idiosyncratic variance. The BIF is a promising estimator for capturing co-movement, but effectively modeling the full high-dimensional volatility landscape demands addressing model specification limitations beyond the factor structure.
\end{abstract}

\tableofcontents

\cleardoublepage
\printnomenclature[2cm]

\cleardoublepage
\pagenumbering{arabic}

\include{introduction}
\include{methodology}
\include{data}
\include{simulation}
\include{empirical}
\include{conclusion}

% \include{contributions}

\printbibliography[heading=bibintoc,title=References]

\appendix
\setlength{\parindent}{0pt}
\include{appendix}

\end{document}